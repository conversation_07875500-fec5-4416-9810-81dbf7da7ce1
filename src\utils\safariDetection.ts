/**
 * كشف متصفح Safari وتطبيق الإعدادات المناسبة
 */

export interface BrowserInfo {
  isSafari: boolean;
  isIOS: boolean;
  isMobile: boolean;
  version: string;
  supportsFeature: (feature: string) => boolean;
}

/**
 * كشف متصفح Safari
 */
export const detectSafari = (): BrowserInfo => {
  const userAgent = navigator.userAgent;
  const vendor = navigator.vendor;
  
  // كشف Safari
  const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent) ||
                   (vendor && vendor.includes('Apple'));
  
  // كشف iOS
  const isIOS = /iPad|iPhone|iPod/.test(userAgent) ||
                (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
  
  // كشف الجوال
  const isMobile = /Mobi|Android/i.test(userAgent) || isIOS;
  
  // استخراج الإصدار
  let version = 'unknown';
  if (isSafari) {
    const versionMatch = userAgent.match(/Version\/(\d+\.\d+)/);
    if (versionMatch) {
      version = versionMatch[1];
    }
  }
  
  // فحص دعم الميزات
  const supportsFeature = (feature: string): boolean => {
    switch (feature) {
      case 'webgl':
        try {
          const canvas = document.createElement('canvas');
          return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
        } catch {
          return false;
        }
      
      case 'geolocation':
        return 'geolocation' in navigator;
      
      case 'notifications':
        return 'Notification' in window;
      
      case 'serviceWorker':
        return 'serviceWorker' in navigator;
      
      case 'webAssembly':
        return 'WebAssembly' in window;
      
      case 'intersectionObserver':
        return 'IntersectionObserver' in window;
      
      case 'resizeObserver':
        return 'ResizeObserver' in window;
      
      case 'customElements':
        return 'customElements' in window;
      
      case 'shadowDOM':
        return 'attachShadow' in Element.prototype;
      
      default:
        return false;
    }
  };
  
  return {
    isSafari,
    isIOS,
    isMobile,
    version,
    supportsFeature
  };
};

/**
 * تطبيق إعدادات خاصة بـ Safari
 */
export const applySafariSettings = (browserInfo: BrowserInfo): void => {
  if (!browserInfo.isSafari) return;
  
  try {
    // إضافة class خاص بـ Safari
    document.documentElement.classList.add('safari-browser');
    
    if (browserInfo.isIOS) {
      document.documentElement.classList.add('ios-device');
    }
    
    if (browserInfo.isMobile) {
      document.documentElement.classList.add('mobile-device');
    }
    
    // إعدادات خاصة بـ Safari
    const style = document.createElement('style');
    style.textContent = `
      /* إعدادات خاصة بـ Safari */
      .safari-browser {
        -webkit-text-size-adjust: 100%;
        -webkit-font-smoothing: antialiased;
      }
      
      .safari-browser .no-select {
        -webkit-user-select: none;
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent;
      }
      
      .ios-device {
        -webkit-overflow-scrolling: touch;
      }
      
      .mobile-device {
        -webkit-tap-highlight-color: transparent;
      }
    `;
    document.head.appendChild(style);
    
    // إعدادات viewport خاصة بـ iOS
    if (browserInfo.isIOS) {
      let viewport = document.querySelector('meta[name="viewport"]');
      if (!viewport) {
        viewport = document.createElement('meta');
        viewport.setAttribute('name', 'viewport');
        document.head.appendChild(viewport);
      }
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
      );
    }
    
    // تحسين الأداء في Safari
    if (browserInfo.isSafari) {
      // تفعيل تسريع الأجهزة
      document.body.style.transform = 'translateZ(0)';
      document.body.style.webkitTransform = 'translateZ(0)';
    }
    
    console.log(`Safari settings applied (Version: ${browserInfo.version}, iOS: ${browserInfo.isIOS})`);
  } catch (error) {
    console.warn('Failed to apply Safari settings:', error);
  }
};

/**
 * فحص التوافق مع Safari
 */
export const checkSafariCompatibility = (browserInfo: BrowserInfo): string[] => {
  const warnings: string[] = [];
  
  if (!browserInfo.isSafari) return warnings;
  
  // فحص الميزات المطلوبة
  const requiredFeatures = [
    'webgl',
    'geolocation',
    'intersectionObserver'
  ];
  
  requiredFeatures.forEach(feature => {
    if (!browserInfo.supportsFeature(feature)) {
      warnings.push(`Feature not supported: ${feature}`);
    }
  });
  
  // فحص الإصدار
  const versionNumber = parseFloat(browserInfo.version);
  if (versionNumber < 14) {
    warnings.push('Safari version is too old. Please update to version 14 or later.');
  }
  
  // تحذيرات خاصة بـ iOS
  if (browserInfo.isIOS && versionNumber < 14) {
    warnings.push('iOS Safari version is too old. Some features may not work properly.');
  }
  
  return warnings;
};

/**
 * تهيئة إعدادات Safari
 */
export const initializeSafariSupport = (): BrowserInfo => {
  const browserInfo = detectSafari();
  
  if (browserInfo.isSafari) {
    applySafariSettings(browserInfo);
    
    const warnings = checkSafariCompatibility(browserInfo);
    if (warnings.length > 0) {
      console.warn('Safari compatibility warnings:', warnings);
    }
    
    console.log('🍎 Safari support initialized successfully');
  }
  
  return browserInfo;
};

export default {
  detectSafari,
  applySafariSettings,
  checkSafariCompatibility,
  initializeSafariSupport
};
