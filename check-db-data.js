import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://jtnqcyouncjoebqcalzh.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0bnFjeW91bmNqb2VicWNhbHpoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU1MTkyMTIsImV4cCI6MjA2MTA5NTIxMn0.VWK5DvW4LxFDLZ-RYaQXDABUaPM8y2vGFnXgKwGZ9Dk'
);

async function checkStations() {
  try {
    console.log('🔍 Checking station data...\n');
    
    const { data, error } = await supabase
      .from('stations')
      .select('id, name, region, latitude, longitude')
      .limit(10);
    
    if (error) {
      console.error('❌ Error:', error);
      return;
    }
    
    console.log(`📊 Found ${data.length} stations\n`);
    
    let validCount = 0;
    let invalidCount = 0;
    let zeroCount = 0;
    
    data.forEach((station, index) => {
      const lat = Number(station.latitude);
      const lng = Number(station.longitude);
      const isValid = !isNaN(lat) && !isNaN(lng) && 
                     lat >= -90 && lat <= 90 && 
                     lng >= -180 && lng <= 180;
      const isZero = lat === 0 && lng === 0;
      
      if (isZero) zeroCount++;
      else if (isValid) validCount++;
      else invalidCount++;
      
      console.log(`${index + 1}. ${station.name}`);
      console.log(`   ID: ${station.id}`);
      console.log(`   Region: ${station.region}`);
      console.log(`   Latitude: ${station.latitude} (${typeof station.latitude}) → ${lat}`);
      console.log(`   Longitude: ${station.longitude} (${typeof station.longitude}) → ${lng}`);
      console.log(`   Status: ${isZero ? '⚠️  ZERO COORDINATES' : isValid ? '✅ VALID' : '❌ INVALID'}`);
      console.log('');
    });
    
    console.log('📈 Summary:');
    console.log(`   Valid coordinates: ${validCount}`);
    console.log(`   Zero coordinates: ${zeroCount}`);
    console.log(`   Invalid coordinates: ${invalidCount}`);
    console.log(`   Total: ${data.length}`);
    
    // Check if we have any valid coordinates for Saudi Arabia
    const saudiStations = data.filter(s => {
      const lat = Number(s.latitude);
      const lng = Number(s.longitude);
      return lat >= 16 && lat <= 32 && lng >= 34 && lng <= 56; // Saudi Arabia bounds
    });
    
    console.log(`\n🇸🇦 Stations within Saudi Arabia bounds: ${saudiStations.length}`);
    
  } catch (err) {
    console.error('❌ Error:', err);
  }
}

checkStations();
