# تعليمات تشغيل سكريبت تطبيق سياسات أمان الصفوف

## متطلبات التشغيل

1. تأكد من تثبيت Node.js على جهازك.
2. تأكد من تثبيت حزمة `@supabase/supabase-js`:
   ```bash
   npm install @supabase/supabase-js
   ```

## طريقة التشغيل

1. افتح موجه الأوامر (Command Prompt) أو Terminal.
2. انتقل إلى مجلد المشروع:
   ```bash
   cd path/to/your/project
   ```
3. قم بتشغيل السكريبت:
   ```bash
   node scripts/apply-rls.js
   ```

## ماذا يفعل السكريبت؟

يقوم السكريبت بتطبيق سياسات أمان الصفوف (Row-Level Security) لجدول المدن في قاعدة البيانات Supabase. هذه السياسات تسمح بما يلي:

1. السماح للجميع بقراءة المدن.
2. السماح للمشرفين والمالكين فقط بإضافة مدن جديدة.
3. السماح للمشرفين والمالكين فقط بتعديل المدن.
4. السماح للمشرفين والمالكين فقط بحذف المدن.
5. سياسة شاملة للسماح للمالك بإدارة المدن (قراءة، إضافة، تعديل، حذف).

## ملاحظات هامة

- يستخدم السكريبت مفتاح الخدمة (service role key) للوصول إلى قاعدة البيانات، لذا يجب الحفاظ على سرية هذا المفتاح.
- يجب تشغيل السكريبت مرة واحدة فقط لتطبيق السياسات.
- إذا كنت ترغب في تعديل السياسات، قم بتعديل ملف السكريبت وتشغيله مرة أخرى.

## استكشاف الأخطاء وإصلاحها

إذا واجهت أي مشكلة أثناء تشغيل السكريبت، تحقق من:

1. أن معلومات المشروع (URL ومفتاح الخدمة) صحيحة.
2. أن لديك اتصال بالإنترنت.
3. أن حزمة `@supabase/supabase-js` مثبتة بشكل صحيح.

إذا استمرت المشكلة، يمكنك تطبيق السياسات يدويًا من خلال لوحة تحكم Supabase كما هو موضح في ملف `supabase/APPLY_RLS.md`.
