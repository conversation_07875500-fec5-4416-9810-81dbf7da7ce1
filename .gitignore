# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build
/dist

# misc
.DS_Store
.env
.env.local
.env.development.local
.env.development
.env.test.local
.env.production.local
.env.production
.env.*
# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.idea/
.vscode/
*.swp
*.swo

# Supabase
/supabase/.temp
/supabase/.env

# Security
*.pem
*.key
*.cert
*.crt
*.p12
*.pfx
*.keystore
*.jks

# Build files
*.js.map
*.d.ts
