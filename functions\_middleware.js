// Cloudflare Pages middleware for SPA routing
export async function onRequest(context) {
  const { request, next } = context;
  const url = new URL(request.url);
  
  // Handle static assets
  if (url.pathname.startsWith('/assets/') || 
      url.pathname.startsWith('/lovable-uploads/') ||
      url.pathname.includes('.') && !url.pathname.endsWith('/')) {
    return next();
  }
  
  // For all other routes, serve index.html (SPA routing)
  if (url.pathname !== '/' && !url.pathname.startsWith('/api/')) {
    const indexUrl = new URL('/', url.origin);
    return fetch(indexUrl.toString());
  }
  
  return next();
}
