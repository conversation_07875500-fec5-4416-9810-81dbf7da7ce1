/* Contact Form Enhancements */

.contact-form-container {
  position: relative;
  overflow: hidden;
}

.contact-form-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, 
    rgba(139, 69, 19, 0.05) 0%, 
    transparent 25%, 
    transparent 75%, 
    rgba(255, 165, 0, 0.05) 100%);
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.form-field-wrapper {
  position: relative;
  transition: all 0.3s ease;
}

.form-field-wrapper:hover {
  transform: translateY(-2px);
}

.form-field-wrapper:focus-within {
  transform: translateY(-2px);
  filter: drop-shadow(0 10px 20px rgba(139, 69, 19, 0.1));
}

.floating-label {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: left top;
}

.form-input:focus + .floating-label,
.form-input:not(:placeholder-shown) + .floating-label {
  transform: translateY(-1.5rem) scale(0.85);
  color: #8b4513;
  font-weight: 500;
}

.submit-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(139, 69, 19, 0.3);
}

.success-message {
  animation: slideInFromTop 0.5s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.character-counter {
  font-size: 0.75rem;
  color: #6b7280;
  transition: color 0.3s ease;
}

.character-counter.warning {
  color: #f59e0b;
}

.character-counter.danger {
  color: #ef4444;
}

/* Phone input styling */
.phone-prefix {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-right: 1px solid #d1d5db;
  font-weight: 600;
  color: #374151;
}

/* Map container styling */
.map-container {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.map-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.map-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    rgba(139, 69, 19, 0.1) 0%, 
    transparent 50%, 
    rgba(255, 165, 0, 0.1) 100%);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.map-container:hover::before {
  opacity: 1;
}

/* Contact info cards */
.contact-info-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.contact-info-card:hover::before {
  left: 100%;
}

.contact-info-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .contact-form-container {
    padding: 1rem;
  }
  
  .form-field-wrapper:hover,
  .form-field-wrapper:focus-within {
    transform: none;
  }
  
  .map-container:hover {
    transform: none;
  }
  
  .contact-info-card:hover {
    transform: translateY(-2px) scale(1.01);
  }
}

/* Loading animation */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Form validation styles */
.form-input.error {
  border-color: #ef4444;
  background-color: #fef2f2;
}

.form-input.success {
  border-color: #10b981;
  background-color: #f0fdf4;
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
