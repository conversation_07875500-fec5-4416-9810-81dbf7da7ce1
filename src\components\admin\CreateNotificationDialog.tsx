import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Bell } from 'lucide-react';
import CreateNotificationForm from './CreateNotificationForm';

interface CreateNotificationDialogProps {
  triggerClassName?: string;
  onNotificationCreated?: () => void;
}

const CreateNotificationDialog = ({ 
  triggerClassName,
  onNotificationCreated 
}: CreateNotificationDialogProps) => {
  const [open, setOpen] = React.useState(false);

  const handleSuccess = () => {
    setOpen(false);
    if (onNotificationCreated) {
      onNotificationCreated();
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="secondary" 
          className={triggerClassName}
        >
          <Bell className="mr-2" size={18} />
          إر<PERSON>ال إشعار جديد
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>إرسال إشعار</DialogTitle>
        </DialogHeader>
        <CreateNotificationForm onSuccess={handleSuccess} />
      </DialogContent>
    </Dialog>
  );
};

export default CreateNotificationDialog;
