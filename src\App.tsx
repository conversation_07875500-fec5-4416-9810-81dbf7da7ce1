import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useEffect, useState, Suspense, lazy } from "react";
import WhatsAppButton from "./components/WhatsAppButton";
import { soundService } from "@/services/SoundService";
import PermissionsDialog from "@/components/permissions/PermissionsDialog";
import LoadingIndicator from "@/components/ui/loading-indicator";
import logger from "@/utils/logger";
import securityUtils from "@/utils/securityUtils";
import { fixUpdateStationFunction } from "@/integrations/supabase/client";
import sessionManager from "@/utils/sessionManager";
import { initializeAdvancedProtection } from "@/utils/advancedProtection";
import { initializeEnvironmentProtection } from "@/utils/environmentProtection";

// Lazy load auth components
const AuthGuard = lazy(() => import(/* webpackChunkName: "admin" */ "@/components/admin/AuthGuard"));

// Lazy load public pages
const Index = lazy(() => import("./pages/Index"));
const Services = lazy(() => import("./pages/Services"));
const Contact = lazy(() => import("./pages/Contact"));
const NotFound = lazy(() => import("./pages/NotFound"));

// Lazy load admin pages as a separate chunk
const AdminPages = {
  Login: lazy(() => import(/* webpackChunkName: "admin" */ "./pages/Admin/Login")),
  Dashboard: lazy(() => import(/* webpackChunkName: "admin" */ "./pages/Admin/Dashboard")),
  UserManagement: lazy(() => import(/* webpackChunkName: "admin" */ "./pages/Admin/UserManagement")),
  Profile: lazy(() => import(/* webpackChunkName: "admin" */ "./pages/Admin/Profile")),
  DatabaseManagement: lazy(() => import(/* webpackChunkName: "admin" */ "./pages/Admin/DatabaseManagement")),
  RegionsManagement: lazy(() => import(/* webpackChunkName: "admin" */ "./pages/Admin/RegionsManagement")),
  Feedbacks: lazy(() => import(/* webpackChunkName: "admin" */ "./pages/Admin/Feedbacks")),
};

// Lazy load security examples separately
const SecurityExamples = lazy(() => import(/* webpackChunkName: "security" */ "./pages/SecurityExamples"));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 دقائق
      gcTime: 10 * 60 * 1000, // 10 دقائق
      retry: 1,
    },
  },
});

const AdminRoute = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<LoadingIndicator className="min-h-screen" />}>
    {children}
  </Suspense>
);

const App = () => {
  const [showPermissionDialog, setShowPermissionDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // تهيئة التطبيق بشكل تدريجي لتحسين الأداء
    const initializeApp = async () => {
      try {
        await Promise.all([
          securityUtils.secureAppInitialization(),
          sessionManager.initializeSession(),
          fixUpdateStationFunction(),
        ]);

        // تهيئة الحماية المتقدمة
        initializeAdvancedProtection();
        initializeEnvironmentProtection();
      } catch (error) {
        logger.error('خطأ في تهيئة التطبيق:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />

        {isLoading && <LoadingIndicator />}

        <BrowserRouter>
          <Suspense fallback={<LoadingIndicator />}>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/services" element={<Services />} />
              <Route path="/contact" element={<Contact />} />
              
              {/* Admin Routes */}
              <Route path="/admin/login" element={
                <AdminRoute>
                  <AdminPages.Login />
                </AdminRoute>
              } />
              <Route path="/admin/dashboard" element={
                <AdminRoute>
                  <AuthGuard>
                    <AdminPages.Dashboard />
                  </AuthGuard>
                </AdminRoute>
              } />
              <Route path="/admin/users" element={
                <AdminRoute>
                  <AuthGuard requireOwner={true}>
                    <AdminPages.UserManagement />
                  </AuthGuard>
                </AdminRoute>
              } />
              <Route path="/admin/profile" element={
                <AdminRoute>
                  <AuthGuard>
                    <AdminPages.Profile />
                  </AuthGuard>
                </AdminRoute>
              } />
              <Route path="/admin/database" element={
                <AdminRoute>
                  <AuthGuard requireOwner={true}>
                    <AdminPages.DatabaseManagement />
                  </AuthGuard>
                </AdminRoute>
              } />
              <Route path="/admin/security-examples" element={
                <AdminRoute>
                  <AuthGuard requireOwner={true}>
                    <SecurityExamples />
                  </AuthGuard>
                </AdminRoute>
              } />
              <Route path="/admin/feedbacks" element={
                <AdminRoute>
                  <AuthGuard requireOwner={true}>
                    <AdminPages.Feedbacks />
                  </AuthGuard>
                </AdminRoute>
              } />
              <Route path="/admin/regions" element={
                <AdminRoute>
                  <AuthGuard requireOwner={true}>
                    <AdminPages.RegionsManagement />
                  </AuthGuard>
                </AdminRoute>
              } />
              
              {/* Catch-all route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
          
          <WhatsAppButton />

          {showPermissionDialog && (
            <PermissionsDialog
              onPermissionsGranted={() => {
                console.log('تم منح الأذونات المطلوبة');
                setShowPermissionDialog(false);
              }}
            />
          )}
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
