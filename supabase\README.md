# تعليمات تطبيق الحلول لإضافة المناطق إلى قاعدة البيانات

## الحل 1: تطبيق سياسات أمان الصفوف لجدول المدن

لتطبيق سياسات أمان الصفوف لجدول المدن، قم بتنفيذ الخطوات التالية:

1. تأكد من أن Supabase CLI مثبت على جهازك:
   ```bash
   npm install -g supabase
   ```

2. قم بتسجيل الدخول إلى Supabase:
   ```bash
   supabase login
   ```

3. قم بتطبيق ملف الترحيل:
   ```bash
   supabase db push
   ```

   أو يمكنك تنفيذ الاستعلام SQL مباشرة من لوحة تحكم Supabase:
   - انتقل إلى لوحة تحكم Supabase
   - اختر مشروعك
   - انتقل إلى قسم "SQL Editor"
   - ان<PERSON><PERSON> محتوى ملف `supabase/migrations/20240601000001_cities_direct_access.sql`
   - قم بتنفيذ الاستعلام

## الحل 2: نشر وظيفة Edge Function

لنشر وظيفة Edge Function التي تسمح بإضافة مدينة جديدة، قم بتنفيذ الخطوات التالية:

1. تأكد من أن Supabase CLI مثبت على جهازك:
   ```bash
   npm install -g supabase
   ```

2. قم بتسجيل الدخول إلى Supabase:
   ```bash
   supabase login
   ```

3. قم بربط المشروع المحلي بمشروع Supabase:
   ```bash
   supabase link --project-ref <project-id>
   ```
   يمكنك العثور على `project-id` في إعدادات مشروعك في لوحة تحكم Supabase.

4. قم بنشر الوظيفة:
   ```bash
   supabase functions deploy add-city
   ```

## كيف يعمل الحل

تم تنفيذ حل متعدد المستويات لإضافة المناطق إلى قاعدة البيانات:

1. **المستوى الأول**: محاولة إضافة المنطقة مباشرة إلى قاعدة البيانات باستخدام سياسات أمان الصفوف.
   - يتطلب أن يكون المستخدم مشرفًا أو مالكًا.
   - يتطلب تطبيق ملف الترحيل `20240601000001_cities_direct_access.sql`.

2. **المستوى الثاني**: إذا فشل المستوى الأول، محاولة إضافة المنطقة باستخدام Edge Function.
   - يتجاوز سياسات أمان الصفوف باستخدام مفتاح الخدمة.
   - يتطلب نشر وظيفة `add-city`.

3. **المستوى الثالث**: إذا فشل المستويان الأول والثاني، إضافة المنطقة محليًا فقط.
   - لا يتطلب أي صلاحيات خاصة.
   - المنطقة ستكون متاحة فقط في الجلسة الحالية.

## ملاحظات هامة

- سياسات أمان الصفوف تسمح للجميع بقراءة المدن، ولكن فقط المشرفين والمالكين يمكنهم إضافة أو تعديل أو حذف المدن.
- إذا كنت تواجه مشكلة في الوصول إلى جدول المدن، تأكد من أن المستخدم لديه دور "admin" أو "owner" في جدول `admin_users`.
- يمكنك التحقق من صلاحيات المستخدم الحالي باستخدام الاستعلام التالي:
  ```sql
  SELECT * FROM admin_users WHERE id = auth.uid();
  ```

## استكشاف الأخطاء وإصلاحها

إذا كنت تواجه مشكلة في إضافة المناطق إلى قاعدة البيانات، تحقق من:

1. أن المستخدم مسجل الدخول ولديه دور "admin" أو "owner" في جدول `admin_users`.
2. أن سياسات أمان الصفوف تم تطبيقها بشكل صحيح.
3. أن وظيفة Edge Function تم نشرها بشكل صحيح.
4. أن متغيرات البيئة `VITE_SUPABASE_URL` تم تعيينها بشكل صحيح في ملف `.env`.

يمكنك مراجعة سجلات الوظيفة في لوحة تحكم Supabase للحصول على مزيد من المعلومات حول الأخطاء.
