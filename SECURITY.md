# إرشادات الأمان

## المفاتيح والرموز السرية

1. **لا تقم أبدًا بتضمين المفاتيح السرية في الكود المصدري**:
   - استخدم ملفات `.env.local` للمفاتيح السرية
   - تأكد من إضافة ملفات `.env.local` إلى `.gitignore`

2. **استخدم متغيرات البيئة**:
   - جميع المفاتيح السرية يجب أن تكون في متغيرات البيئة
   - استخدم `process.env.VARIABLE_NAME` للوصول إليها

3. **تجنب تسرب البيانات في وحدة تحكم المتصفح**:
   - استخدم وحدة `logger.ts` بدلاً من `console.log` مباشرة
   - تأكد من عدم طباعة البيانات الحساسة في وحدة التحكم
   - استخدم وحدة `disableConsoleInProduction.ts` لتعطيل وحدة التحكم في بيئة الإنتاج

4. **تشفير البيانات المحلية**:
   - استخدم وحدة `secureStorage.ts` لتخزين البيانات الحساسة محليًا
   - لا تخزن المفاتيح أو الرموز السرية في localStorage بدون تشفير

5. **تعطيل أدوات المطور في بيئة الإنتاج**:
   - استخدم وحدة `disableDevTools.ts` لتعطيل أدوات المطور في بيئة الإنتاج
   - منع المستخدمين من الوصول إلى البيانات الحساسة عبر أدوات المطور

## أمان قاعدة البيانات

1. **استخدم سياسات RLS (Row Level Security)**:
   - تأكد من تطبيق سياسات RLS على جميع الجداول
   - اختبر السياسات للتأكد من أنها تعمل بشكل صحيح

2. **لا تستخدم مفتاح الخدمة في الواجهة الأمامية**:
   - استخدم فقط مفتاح anon في الواجهة الأمامية
   - استخدم مفتاح الخدمة فقط في الخوادم الآمنة أو وظائف Edge

3. **تحقق من صحة البيانات**:
   - تحقق من صحة جميع البيانات المدخلة قبل إرسالها إلى قاعدة البيانات
   - استخدم التحقق على جانب الخادم والعميل

## إنشاء المستخدمين

1. **لا تقم بإنشاء مستخدمين تلقائيًا في الكود**:
   - يجب إنشاء المستخدمين فقط من خلال واجهة المستخدم للمالك
   - أو من خلال قاعدة البيانات مباشرة

2. **تقييد الوصول إلى أدوات الإدارة**:
   - تأكد من أن أدوات الإدارة متاحة فقط للمستخدمين المصرح لهم
   - استخدم AuthGuard مع requireOwner=true للوظائف الحساسة

## تحديثات الأمان

1. **قم بتحديث التبعيات بانتظام**:
   - استخدم `npm audit` للتحقق من الثغرات الأمنية
   - قم بتحديث الحزم التي تحتوي على ثغرات أمنية

2. **راجع الكود بانتظام**:
   - ابحث عن أي تسرب للبيانات الحساسة
   - تأكد من اتباع أفضل ممارسات الأمان

## الوحدات الأمنية المتوفرة

1. **logger.ts**:
   - وحدة تسجيل آمنة تقوم بتصفية البيانات الحساسة
   - استخدمها بدلاً من console.log مباشرة

2. **disableConsoleInProduction.ts**:
   - تعطيل وحدة تحكم المتصفح في بيئة الإنتاج
   - منع تسرب البيانات الحساسة في وحدة التحكم

3. **disableDevTools.ts**:
   - تعطيل أدوات المطور في بيئة الإنتاج
   - منع المستخدمين من الوصول إلى البيانات الحساسة

4. **secureStorage.ts**:
   - تشفير البيانات المخزنة محليًا
   - استخدمها لتخزين البيانات الحساسة بشكل آمن

5. **secureExcel.ts**:
   - استخدام Excel بشكل آمن
   - تعطيل الميزات الخطرة
   - تطهير البيانات المستوردة والمصدرة

6. **secureApi.ts**:
   - تأمين استخدام API
   - تطهير المدخلات والمخرجات
   - التحقق من صلاحيات المستخدم

7. **secureIO.ts**:
   - تأمين المدخلات والمخرجات
   - تطهير النصوص من الرموز الخطرة
   - التحقق من صحة المدخلات

8. **secureFiles.ts**:
   - تأمين التعامل مع الملفات
   - التحقق من أنواع الملفات وأحجامها
   - تطهير أسماء الملفات

9. **secureCommunication.ts**:
   - تأمين الاتصالات
   - إرسال طلبات HTTP آمنة
   - التعامل مع الجلسات بشكل آمن

10. **securityUtils.ts**:
    - وحدة أمان عامة
    - توفير واجهة موحدة لجميع وحدات الأمان
    - تأمين التطبيق عند بدء التشغيل

## أمثلة استخدام وحدات الأمان

### 1. استخدام Excel بشكل آمن

```typescript
import secureExcel from '@/utils/secureExcel';

// قراءة ملف Excel بشكل آمن
const handleFileUpload = async (file: File) => {
  try {
    // التحقق من نوع الملف
    if (!secureExcel.isValidExcelFile(file.name)) {
      throw new Error('نوع ملف غير مدعوم');
    }

    // قراءة الملف بشكل آمن
    const data = await secureExcel.readExcelSecurely(file);

    // استخدام البيانات...
    console.log(data);
  } catch (error) {
    console.error(error);
  }
};

// تصدير بيانات إلى Excel بشكل آمن
const handleExport = (data: any[][]) => {
  try {
    secureExcel.exportToExcelSecurely(data, 'exported_data.xlsx');
  } catch (error) {
    console.error(error);
  }
};
```

### 2. استخدام التخزين الآمن

```typescript
import secureStorage from '@/utils/secureStorage';

// تخزين بيانات حساسة
secureStorage.setItem('user_token', token, true); // إجبار التشفير

// استرجاع البيانات
const token = secureStorage.getItem<string>('user_token', '');

// حذف البيانات
secureStorage.removeItem('user_token');
```

### 3. تطهير المدخلات

```typescript
import secureIO from '@/utils/secureIO';

// تطهير مدخلات النموذج
const sanitizedInput = secureIO.sanitizeFormInput(userInput);

// تطهير HTML مع السماح ببعض الوسوم
const sanitizedHtml = secureIO.sanitizeHTMLAllowSome(htmlContent);

// التحقق من صحة البريد الإلكتروني
if (secureIO.isValidEmail(email)) {
  // البريد الإلكتروني صالح
}
```

### 4. رفع الملفات بشكل آمن

```typescript
import secureFiles from '@/utils/secureFiles';

// رفع ملف بشكل آمن
const handleFileUpload = async (file: File) => {
  try {
    // التحقق من نوع الملف
    if (!secureFiles.isValidFileType(file, secureFiles.SUPPORTED_IMAGE_TYPES)) {
      throw new Error('نوع ملف غير مدعوم');
    }

    // التحقق من حجم الملف
    if (!secureFiles.isValidFileSize(file)) {
      throw new Error('حجم الملف كبير جدًا');
    }

    // رفع الملف
    const fileUrl = await secureFiles.uploadFileSecurely(file, 'public', 'uploads');

    // استخدام رابط الملف...
    console.log(fileUrl);
  } catch (error) {
    console.error(error);
  }
};
```

## استكشاف الأخطاء وإصلاحها

إذا واجهت مشكلة في فتح التطبيق بعد تفعيل وحدات الأمان، اتبع الخطوات التالية:

1. **تعطيل وحدات الأمان مؤقتًا**:
   - قم بتعليق استيراد وحدات الأمان في ملف main.tsx
   - تأكد من أن التطبيق يعمل بشكل صحيح

2. **تفعيل الوحدات واحدة تلو الأخرى**:
   - قم بتفعيل وحدة واحدة في كل مرة
   - اختبر التطبيق بعد كل تفعيل
   - حدد الوحدة التي تسبب المشكلة

3. **تعديل الوحدة المسببة للمشكلة**:
   - قم بتبسيط الوحدة المسببة للمشكلة
   - أضف المزيد من التعليقات التوضيحية
   - اختبر التطبيق مرة أخرى
