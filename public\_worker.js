// <PERSON>flare Worker for proper MIME types
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    
    // Handle CSS files with correct MIME type
    if (url.pathname.endsWith('.css')) {
      const response = await env.ASSETS.fetch(request);
      const newResponse = new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: {
          ...response.headers,
          'Content-Type': 'text/css',
          'Cache-Control': 'public, max-age=31536000, immutable'
        }
      });
      return newResponse;
    }
    
    // Handle JS files with correct MIME type
    if (url.pathname.endsWith('.js')) {
      const response = await env.ASSETS.fetch(request);
      const newResponse = new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: {
          ...response.headers,
          'Content-Type': 'application/javascript',
          'Cache-Control': 'public, max-age=31536000, immutable'
        }
      });
      return newResponse;
    }
    
    // For all other requests, use default behavior
    return env.ASSETS.fetch(request);
  }
};
