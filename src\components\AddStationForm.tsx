import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useToast, toast } from '@/hooks/use-toast';
import { addStation } from '@/services/stationService';
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogClose, DialogDescription } from '@/components/ui/dialog';
import mapboxgl from 'mapbox-gl';
import { extractLatLngFromGoogleMapsUrl } from '@/utils/mapUtils';

// helper class for visually hidden (screen reader only)
const srOnly = 'sr-only';

export const AddStationForm: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [formData, setFormData] = useState({
    name: '',
    region: '',
    sub_region: '',
    latitude: '',
    longitude: '',
    fuel_types: '',
    additional_info: ''
  });
  const [locationMode, setLocationMode] = useState<'link' | 'map' | 'manual'>('manual');
  const [googleMapsUrl, setGoogleMapsUrl] = useState('');
  const [mapDialogOpen, setMapDialogOpen] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [mapMarker, setMapMarker] = useState<{ lat: number; lng: number } | null>(null);

  // حفظ اللغة الحالية عند تحميل المكون
  useEffect(() => {
    const currentLang = i18n.language;
    localStorage.setItem('preferredLanguage', currentLang);
  }, [i18n.language]);

  // استعادة اللغة المحفوظة عند تغيير الصفحة
  useEffect(() => {
    const savedLang = localStorage.getItem('preferredLanguage');
    if (savedLang && savedLang !== i18n.language) {
      i18n.changeLanguage(savedLang);
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const stationData = {
        ...formData,
        latitude: parseFloat(formData.latitude),
        longitude: parseFloat(formData.longitude)
      };

      await addStation(stationData);
      toast({
        title: t('success'),
        description: t('stationAddedSuccessfully'),
      });
      setFormData({
        name: '',
        region: '',
        sub_region: '',
        latitude: '',
        longitude: '',
        fuel_types: '',
        additional_info: ''
      });
    } catch (error) {
      toast({
        title: t('error'),
        description: t('errorAddingStation'),
      });
    }
  };

  // دالة استخراج الإحداثيات من الرابط
  const handleExtractFromUrl = () => {
    const coords = extractLatLngFromGoogleMapsUrl(googleMapsUrl);
    if (coords) {
      setFormData({ ...formData, latitude: coords.latitude.toString(), longitude: coords.longitude.toString() });
      setLocationError(null);
    } else {
      setLocationError(t('invalidGoogleMapsUrl'));
    }
  };

  // دالة فتح نافذة الخريطة
  const handleOpenMapDialog = () => {
    setMapDialogOpen(true);
    setTimeout(() => {
      // تهيئة الخريطة بعد فتح النافذة
      if (!document.getElementById('station-map')) return;
      const map = new mapboxgl.Map({
        container: 'station-map',
        style: 'mapbox://styles/mapbox/streets-v11',
        center: [45.079, 23.885],
        zoom: 5,
        accessToken: (mapboxgl as any).accessToken || process.env.REACT_APP_MAPBOX_TOKEN || process.env.VITE_MAPBOX_TOKEN
      });
      let marker: mapboxgl.Marker | null = null;
      map.on('click', (e) => {
        if (marker) marker.remove();
        marker = new mapboxgl.Marker()
          .setLngLat([e.lngLat.lng, e.lngLat.lat])
          .addTo(map);
        setMapMarker({ lat: e.lngLat.lat, lng: e.lngLat.lng });
      });
    }, 300);
  };

  // عند تأكيد الموقع من الخريطة
  const handleConfirmMapLocation = () => {
    if (mapMarker) {
      setFormData({ ...formData, latitude: mapMarker.lat.toString(), longitude: mapMarker.lng.toString() });
      setLocationError(null);
      setMapDialogOpen(false);
    } else {
      setLocationError(t('pleaseSelectLocationOnMap'));
    }
  };

  // التحقق من صحة الإحداثيات
  const isValidLatLng = (lat: string, lng: string) => {
    const latNum = parseFloat(lat);
    const lngNum = parseFloat(lng);
    return !isNaN(latNum) && !isNaN(lngNum) && latNum >= -90 && latNum <= 90 && lngNum >= -180 && lngNum <= 180;
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* اختيار طريقة تحديد الموقع */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">{t('locationMethod')}</label>
        <div className="flex gap-2">
          <button type="button" className={`px-3 py-1 rounded ${locationMode==='link'?'bg-noor-purple text-white':'bg-gray-100'}`} onClick={()=>setLocationMode('link')}>{t('googleMapsLink')}</button>
          <button type="button" className={`px-3 py-1 rounded ${locationMode==='map'?'bg-noor-purple text-white':'bg-gray-100'}`} onClick={()=>setLocationMode('map')}>{t('chooseOnMap')}</button>
          <button type="button" className={`px-3 py-1 rounded ${locationMode==='manual'?'bg-noor-purple text-white':'bg-gray-100'}`} onClick={()=>setLocationMode('manual')}>{t('manualEntry')}</button>
        </div>
      </div>
      {/* واجهة إدخال الرابط */}
      {locationMode==='link' && (
        <div>
          <label className="block text-sm font-medium text-gray-700">{t('googleMapsLink')}</label>
          <div className="flex gap-2">
            <input type="text" value={googleMapsUrl} onChange={e=>setGoogleMapsUrl(e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm" placeholder="https://maps.google.com/..." />
            <button type="button" className="px-3 py-1 bg-noor-orange text-white rounded" onClick={handleExtractFromUrl}>{t('extractCoordinates')}</button>
          </div>
        </div>
      )}
      {/* واجهة اختيار الموقع على الخريطة */}
      {locationMode==='map' && (
        <div>
          <Dialog open={mapDialogOpen} onOpenChange={setMapDialogOpen}>
            <DialogTrigger asChild>
              <button type="button" className="px-3 py-1 bg-noor-orange text-white rounded" onClick={handleOpenMapDialog}>{t('openMap')}</button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>{t('chooseLocationOnMap') || <span className={srOnly}>Choose location on map</span>}</DialogTitle>
                <DialogDescription>{t('chooseLocationOnMapDesc') || 'اختر موقع المحطة بالنقر على الخريطة ثم اضغط تأكيد.'}</DialogDescription>
              </DialogHeader>
              <div id="station-map" style={{width:'100%',height:'400px',borderRadius:'8px'}}></div>
              <div className="flex justify-end gap-2 mt-4">
                <DialogClose asChild>
                  <button type="button" className="px-3 py-1 bg-gray-200 rounded">{t('cancel')}</button>
                </DialogClose>
                <button type="button" className="px-3 py-1 bg-noor-purple text-white rounded" onClick={handleConfirmMapLocation}>{t('confirm')}</button>
              </div>
            </DialogContent>
          </Dialog>
          {mapMarker && (
            <div className="mt-2 text-sm text-gray-700">{t('selectedCoordinates')}: {mapMarker.lat.toFixed(6)}, {mapMarker.lng.toFixed(6)}</div>
          )}
        </div>
      )}
      {/* واجهة الإدخال اليدوي */}
      {locationMode==='manual' && (
        <div className="flex gap-2">
          <div>
            <label className="block text-sm font-medium text-gray-700">{t('latitude')}</label>
            <input type="number" step="any" value={formData.latitude} onChange={e=>setFormData({...formData, latitude:e.target.value})} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm" />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">{t('longitude')}</label>
            <input type="number" step="any" value={formData.longitude} onChange={e=>setFormData({...formData, longitude:e.target.value})} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm" />
          </div>
        </div>
      )}
      {/* عرض الإحداثيات والمعاينة */}
      <div className="flex gap-2 mt-2">
        <div className="text-sm text-gray-700">{t('latitude')}: {formData.latitude}</div>
        <div className="text-sm text-gray-700">{t('longitude')}: {formData.longitude}</div>
      </div>
      {/* رسالة الخطأ */}
      {locationError && <div className="text-red-600 text-sm mt-1">{locationError}</div>}
      {/* التحقق من صحة الإحداثيات */}
      {!isValidLatLng(formData.latitude, formData.longitude) && <div className="text-red-600 text-sm mt-1">{t('invalidCoordinates')}</div>}
      {/* باقي حقول النموذج */}
      <div>
        <label className="block text-sm font-medium text-gray-700">
          {t('stationName')}
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
        />
      </div>
      {/* باقي الحقول ... */}
    </form>
  );
}; 