/**
 * Safari Compatibility Styles
 * إعدادات خاصة لضمان التوافق مع متصفح Safari
 */

/* منع تحديد النص في Safari */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* السماح بتحديد النص في الحقول */
.selectable,
input,
textarea,
[contenteditable] {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

/* إصلاح مشاكل التمرير في Safari */
.safari-scroll-fix {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* إصلاح مشاكل الخطوط في Safari */
.safari-font-fix {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* إصلاح مشاكل الحدود في Safari */
.safari-border-fix {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* إصلاح مشاكل الظلال في Safari */
.safari-shadow-fix {
  -webkit-box-shadow: var(--shadow);
  box-shadow: var(--shadow);
}

/* إصلاح مشاكل التحولات في Safari */
.safari-transform-fix {
  -webkit-transform: var(--transform);
  transform: var(--transform);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* إصلاح مشاكل الشفافية في Safari */
.safari-opacity-fix {
  -webkit-opacity: var(--opacity);
  opacity: var(--opacity);
}

/* إعدادات خاصة للحماية في Safari */
@supports (-webkit-appearance: none) {
  /* هذا سيطبق فقط في Safari */
  
  body {
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* السماح بالتفاعل مع العناصر المهمة */
  button,
  a,
  input,
  textarea,
  select,
  [role="button"],
  [tabindex] {
    -webkit-user-select: auto;
    -webkit-touch-callout: default;
  }
  
  /* إخفاء شريط التمرير في Safari */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }
}

/* إصلاح مشاكل الفيديو في Safari */
video {
  -webkit-playsinline: true;
  playsinline: true;
}

/* إصلاح مشاكل الصور في Safari */
img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* إصلاح مشاكل الروابط في Safari */
a {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* إعدادات خاصة للخريطة في Safari */
.mapboxgl-map {
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* إصلاح مشاكل النماذج في Safari */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
textarea {
  -webkit-appearance: none;
  -webkit-border-radius: 0;
  border-radius: 0;
}

/* إصلاح مشاكل الأزرار في Safari */
button {
  -webkit-appearance: none;
  -webkit-border-radius: 0;
  border-radius: 0;
  cursor: pointer;
}

/* إعدادات الطباعة في Safari */
@media print {
  body {
    display: none !important;
  }
  
  body::before {
    content: "الطباعة غير مسموحة لأسباب أمنية";
    display: block;
    text-align: center;
    font-size: 24px;
    margin-top: 50px;
  }
}

/* إعدادات الوضع المظلم في Safari */
@media (prefers-color-scheme: dark) {
  .safari-dark-mode {
    color-scheme: dark;
  }
}

/* إصلاح مشاكل الحركة في Safari */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
