#!/usr/bin/env node

// Build script for Cloudflare Pages
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 Starting build process...');

try {
  // Check if vite is available
  console.log('📦 Checking dependencies...');
  
  // Install dependencies if needed
  if (!fs.existsSync('node_modules/vite')) {
    console.log('📥 Installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });
  }
  
  // Run vite build
  console.log('🔨 Building with Vite...');
  execSync('npx vite build', { stdio: 'inherit' });
  
  // Copy Cloudflare files
  console.log('📋 Copying Cloudflare files...');

  const filesToCopy = [
    { src: 'public/_headers', dest: 'dist/_headers' },
    { src: 'public/_redirects', dest: 'dist/_redirects' },
    { src: 'public/_worker.js', dest: 'dist/_worker.js' }
  ];

  filesToCopy.forEach(({ src, dest }) => {
    try {
      if (fs.existsSync(src)) {
        fs.copyFileSync(src, dest);
        console.log(`✅ Copied ${src} to ${dest}`);
      } else {
        console.log(`⚠️ Source file ${src} not found, skipping...`);
      }
    } catch (error) {
      console.log(`⚠️ Failed to copy ${src}: ${error.message}`);
    }
  });
  
  console.log('✅ Build completed successfully!');
  
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
