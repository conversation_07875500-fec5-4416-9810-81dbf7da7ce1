<!DOCTYPE html>
<html>
<head>
    <title>Debug Station Coordinates</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Station Coordinates Debug</h1>
    <div id="output"></div>
    
    <script>
        const supabaseUrl = 'https://jtnqcyouncjoebqcalzh.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0bnFjeW91bmNqb2VicWNhbHpoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTQ5MDU2NzQsImV4cCI6MjAzMDQ4MTY3NH0.Ej_2Zt8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        async function fetchStations() {
            try {
                const { data, error } = await supabase
                    .from('stations')
                    .select('*')
                    .limit(20);
                
                if (error) {
                    document.getElementById('output').innerHTML = `<p>Error: ${error.message}</p>`;
                    return;
                }
                
                let html = '<h2>Station Coordinates Analysis:</h2>';
                let validCount = 0;
                let invalidCount = 0;
                
                data.forEach(station => {
                    const lat = Number(station.latitude);
                    const lng = Number(station.longitude);
                    const isValid = !isNaN(lat) && !isNaN(lng) && 
                                   lat >= -90 && lat <= 90 && 
                                   lng >= -180 && lng <= 180 &&
                                   lat !== 0 && lng !== 0;
                    
                    if (isValid) validCount++;
                    else invalidCount++;
                    
                    html += `
                        <div style="border: 1px solid ${isValid ? 'green' : 'red'}; margin: 10px; padding: 10px; background: ${isValid ? '#f0fff0' : '#fff0f0'};">
                            <h3>${station.name}</h3>
                            <p><strong>ID:</strong> ${station.id}</p>
                            <p><strong>Region:</strong> ${station.region}</p>
                            <p><strong>Latitude:</strong> ${station.latitude} → ${lat} (Type: ${typeof station.latitude})</p>
                            <p><strong>Longitude:</strong> ${station.longitude} → ${lng} (Type: ${typeof station.longitude})</p>
                            <p><strong>Valid:</strong> ${isValid ? '✅ YES' : '❌ NO'}</p>
                            ${!isValid ? `<p><strong>Issues:</strong> 
                                ${isNaN(lat) ? 'Invalid latitude, ' : ''}
                                ${isNaN(lng) ? 'Invalid longitude, ' : ''}
                                ${lat === 0 && lng === 0 ? 'Zero coordinates, ' : ''}
                                ${lat < -90 || lat > 90 ? 'Latitude out of range, ' : ''}
                                ${lng < -180 || lng > 180 ? 'Longitude out of range' : ''}
                            </p>` : ''}
                        </div>
                    `;
                });
                
                html = `<div style="background: #e6f3ff; padding: 10px; margin-bottom: 20px;">
                    <h3>Summary:</h3>
                    <p>Valid stations: ${validCount}</p>
                    <p>Invalid stations: ${invalidCount}</p>
                    <p>Total: ${data.length}</p>
                </div>` + html;
                
                document.getElementById('output').innerHTML = html;
            } catch (err) {
                document.getElementById('output').innerHTML = `<p>Error: ${err.message}</p>`;
            }
        }
        
        fetchStations();
    </script>
</body>
</html>
