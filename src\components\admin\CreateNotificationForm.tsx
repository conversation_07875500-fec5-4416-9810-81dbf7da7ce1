import React, { useState } from 'react';
import { useForm } from "react-hook-form";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

interface NotificationFormProps {
  onSuccess?: () => void;
  onSubmit?: (title: string, message: string) => void;
}

interface FormValues {
  title: string;
  message: string;
}

const CreateNotificationForm: React.FC<NotificationFormProps> = ({ onSubmit, onSuccess }) => {
  const [error, setError] = useState('');
  const { toast } = useToast();
  const form = useForm<FormValues>({
    defaultValues: {
      title: '',
      message: ''
    }
  });

  const handleSubmit = form.handleSubmit((data) => {
    if (!data.title.trim() || !data.message.trim()) {
      setError('الرجاء تعبئة جميع الحقول.');
      return;
    }

    if (onSubmit) {
      onSubmit(data.title, data.message);
    }
    
    if (onSuccess) {
      onSuccess();
    }

    // Reset form and show success toast
    form.reset();
    setError('');
    toast({
      title: "تم بنجاح",
      description: "تم إرسال الإشعار بنجاح",
      variant: "default",
    });
  });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>عنوان الإشعار</FormLabel>
              <FormControl>
                <Input placeholder="أدخل عنوان الإشعار" {...field} />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem>
              <FormLabel>محتوى الإشعار</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="أدخل محتوى الإشعار"
                  rows={4}
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {error && (
          <div className="text-red-500 text-sm">{error}</div>
        )}

        <Button type="submit" className="w-full">
          إرسال الإشعار
        </Button>
      </form>
    </Form>
  );
};

export default CreateNotificationForm;
