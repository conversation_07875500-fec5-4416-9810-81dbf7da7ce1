// React compatibility shim for Cloudflare Pages
(function() {
  'use strict';
  
  // تأكد من وجود React في النطاق العام
  if (typeof window.React === 'undefined') {
    // إنشاء React mock مؤقت حتى يتم تحميل React الحقيقي
    window.React = {
      createContext: function(defaultValue) {
        console.warn('React.createContext called before React is loaded');
        return {
          Provider: function() { return null; },
          Consumer: function() { return null; },
          displayName: 'Context'
        };
      },
      createElement: function() {
        console.warn('React.createElement called before React is loaded');
        return null;
      },
      Component: function() {
        console.warn('React.Component called before React is loaded');
      },
      Fragment: 'Fragment',
      StrictMode: function() { return null; },
      useState: function() {
        console.warn('React.useState called before React is loaded');
        return [null, function() {}];
      },
      useEffect: function() {
        console.warn('React.useEffect called before React is loaded');
      },
      useContext: function() {
        console.warn('React.useContext called before React is loaded');
        return null;
      }
    };
    
    // إضافة علامة للإشارة إلى أن هذا mock
    window.React.__isMock = true;
  }
  
  // تأكد من وجود ReactDOM
  if (typeof window.ReactDOM === 'undefined') {
    window.ReactDOM = {
      render: function() {
        console.warn('ReactDOM.render called before ReactDOM is loaded');
      },
      createRoot: function() {
        console.warn('ReactDOM.createRoot called before ReactDOM is loaded');
        return {
          render: function() {
            console.warn('root.render called before ReactDOM is loaded');
          }
        };
      }
    };
    
    window.ReactDOM.__isMock = true;
  }
  
  console.log('React shim loaded successfully');
})();
