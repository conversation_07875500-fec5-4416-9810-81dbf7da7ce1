import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Toaster } from "@/components/ui/toaster";
import { useToast } from "@/hooks/use-toast";
import Header from "@/components/Header";
import { useLanguage } from "@/i18n/LanguageContext";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import {
  UserCircle,
  Mail,
  Phone,
  MapPin,
  Clock,
  Send,
  MessageCircle,
  Globe,
  Shield,
  Zap,
  Users,
  Star,
  ArrowRight,
  CheckCircle
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import FeedbackForm from '@/components/FeedbackForm';
import '@/styles/contact-form.css';

const Contact = () => {
  const { language, t } = useLanguage();
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form data
      if (!formData.name.trim() || !formData.email.trim() || !formData.phone.trim() || !formData.message.trim()) {
        toast({
          title: language === 'ar' ? 'خطأ في البيانات' : 'Validation Error',
          description: language === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill in all required fields',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        toast({
          title: language === 'ar' ? 'خطأ في البريد الإلكتروني' : 'Invalid Email',
          description: language === 'ar' ? 'يرجى إدخال بريد إلكتروني صحيح' : 'Please enter a valid email address',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // Validate phone number (should be 9 digits)
      if (formData.phone.length !== 9 || !/^\d{9}$/.test(formData.phone)) {
        toast({
          title: language === 'ar' ? 'خطأ في رقم الهاتف' : 'Invalid Phone Number',
          description: language === 'ar' ? 'يرجى إدخال رقم هاتف صحيح (9 أرقام)' : 'Please enter a valid phone number (9 digits)',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // Here you can add actual email sending logic
      // For now, we'll simulate the submission
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast({
        title: language === 'ar' ? 'تم إرسال رسالتك بنجاح' : 'Your message has been sent successfully',
        description: language === 'ar' ? 'سنتواصل معك قريبًا' : 'We will contact you soon',
      });

      setFormData({
        name: '',
        email: '',
        phone: '',
        message: '',
      });

      setIsSubmitted(true);
      setTimeout(() => setIsSubmitted(false), 5000);

      // إظهار بطاقة التقييم المنبثقة بعد إرسال الرسالة
      setTimeout(() => setShowFeedbackDialog(true), 2000);
    } catch (error) {
      toast({
        title: language === 'ar' ? 'خطأ في الإرسال' : 'Sending Error',
        description: language === 'ar' ? 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى' : 'An error occurred while sending the message. Please try again',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      icon: <Phone size={28} />,
      titleAr: 'اتصل بنا',
      titleEn: 'Call Us',
      contentAr: '+966 8003033313',
      contentEn: '+966 8003033313',
      descriptionAr: 'متاح على مدار الساعة',
      descriptionEn: 'Available 24/7',
      color: 'from-green-500 to-emerald-600',
      onClick: () => window.open('tel:+9668003033313', '_self'),
    },
    {
      icon: <Mail size={28} />,
      titleAr: 'البريد الإلكتروني',
      titleEn: 'Email',
      contentAr: '<EMAIL>',
      contentEn: '<EMAIL>',
      descriptionAr: 'نرد خلال 24 ساعة',
      descriptionEn: 'We reply within 24 hours',
      color: 'from-blue-500 to-cyan-600',
      onClick: () => window.open('mailto:<EMAIL>', '_self'),
    },
    {
      icon: <MapPin size={28} />,
      titleAr: 'العنوان',
      titleEn: 'Address',
      contentAr: 'نجران، المملكة العربية السعودية',
      contentEn: 'Najran, Saudi Arabia',
      descriptionAr: 'مقر الشركة الرئيسي',
      descriptionEn: 'Main headquarters',
      color: 'from-purple-500 to-violet-600',
      onClick: () => {
        if (confirm(language === 'ar' ? 'هل تود الانتقال إلى موقع الشركة على خريطة Google؟' : 'Would you like to go to our location on Google Maps?')) {
          window.open(
            'https://www.google.com/maps/place/%D8%A7%D9%84%D9%86%D9%88%D8%B1+%D8%A7%D9%84%D9%85%D8%AA%D8%AD%D8%AF%D8%A9%E2%80%AD/@17.5670747,44.3335683,91m/data=!3m1!1e3!4m6!3m5!1s0x15fed944c5b99bad:0xeb9ef53c79cfa0d!8m2!3d17.5670984!4d44.3337896!16s%2Fg%2F11f5d3dzfm?entry=ttu&g_ep=EgoyMDI1MDcxNi4wIKXMDSoASAFQAw%3D%3D',
            '_blank'
          );
        }
      },
    },
    {
      icon: <Clock size={28} />,
      titleAr: 'ساعات العمل',
      titleEn: 'Working Hours',
      contentAr: '24 ساعة / 7 أيام في الأسبوع',
      contentEn: '24 hours / 7 days a week',
      descriptionAr: 'خدمة مستمرة',
      descriptionEn: 'Continuous service',
      color: 'from-orange-500 to-red-600',
    },
  ];

  const features = [
    {
      icon: <Shield size={32} />,
      titleAr: 'أمان وموثوقية',
      titleEn: 'Security & Reliability',
      descriptionAr: 'نضمن أمان بياناتك وخصوصيتك',
      descriptionEn: 'We guarantee your data security and privacy'
    },
    {
      icon: <Zap size={32} />,
      titleAr: 'استجابة سريعة',
      titleEn: 'Quick Response',
      descriptionAr: 'نرد على استفساراتك في أسرع وقت',
      descriptionEn: 'We respond to your inquiries as quickly as possible'
    },
    {
      icon: <Users size={32} />,
      titleAr: 'فريق متخصص',
      titleEn: 'Expert Team',
      descriptionAr: 'فريق من الخبراء لخدمتك',
      descriptionEn: 'A team of experts at your service'
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen flex flex-col bg-gradient-to-br from-gray-50 via-white to-blue-50"
    >
      <Toaster />
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-noor-purple/10 via-transparent to-noor-orange/10"></div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-noor-purple to-noor-orange bg-clip-text text-transparent">
              {language === 'ar' ? 'تواصل معنا' : 'Get In Touch'}
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed">
              {language === 'ar'
                ? 'نحن هنا لمساعدتك في كل ما تحتاجه. تواصل معنا وسنكون سعداء للرد على استفساراتك'
                : 'We are here to help you with everything you need. Contact us and we will be happy to answer your questions'
              }
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="flex items-center gap-2 bg-white px-6 py-3 rounded-full shadow-lg"
              >
                <Star className="text-yellow-500" size={20} />
                <span className="text-gray-700 font-medium">
                  {language === 'ar' ? 'خدمة عملاء متميزة' : 'Excellent Customer Service'}
                </span>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="flex items-center gap-2 bg-white px-6 py-3 rounded-full shadow-lg"
              >
                <CheckCircle className="text-green-500" size={20} />
                <span className="text-gray-700 font-medium">
                  {language === 'ar' ? 'استجابة سريعة' : 'Quick Response'}
                </span>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      <main className="flex-grow container mx-auto px-4 md:px-6 pb-12">
        {/* Features Section */}
        <motion.section
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-16"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="text-center p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-noor-purple to-noor-orange rounded-full text-white mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold mb-2 text-gray-800">
                  {language === 'ar' ? feature.titleAr : feature.titleEn}
                </h3>
                <p className="text-gray-600">
                  {language === 'ar' ? feature.descriptionAr : feature.descriptionEn}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
          {/* Contact Information */}
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <Card className="mb-8 border-0 shadow-2xl bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-6">
                <CardTitle className="text-2xl font-bold text-center bg-gradient-to-r from-noor-purple to-noor-orange bg-clip-text text-transparent">
                  {language === 'ar' ? 'معلومات الاتصال' : 'Contact Information'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {contactInfo.map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      whileHover={{ scale: 1.02 }}
                      className={`group relative p-6 rounded-2xl bg-gradient-to-r ${item.color} cursor-pointer overflow-hidden contact-info-card`}
                      onClick={item.onClick}
                    >
                      <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative z-10 flex items-start gap-4 text-white">
                        <div className="flex-shrink-0 p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                          {item.icon}
                        </div>
                        <div className="flex-grow">
                          <h3 className="text-lg font-bold mb-1">
                            {language === 'ar' ? item.titleAr : item.titleEn}
                          </h3>
                          <p className="text-white/90 font-medium mb-1">
                            {language === 'ar' ? item.contentAr : item.contentEn}
                          </p>
                          <p className="text-white/70 text-sm">
                            {language === 'ar' ? item.descriptionAr : item.descriptionEn}
                          </p>
                        </div>
                        {item.onClick && (
                          <ArrowRight className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" size={20} />
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Map Section */}
            <Card className="border-0 shadow-2xl bg-white/80 backdrop-blur-sm overflow-hidden">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold text-center text-gray-800">
                  {language === 'ar' ? 'موقعنا على الخريطة' : 'Find Us on Map'}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="bg-white rounded-2xl shadow-xl overflow-hidden map-container">
                  <div className="h-96 w-full">
                    <iframe
                      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3654.234567890123!2d44.2267!3d17.4925!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTfCsDI5JzMzLjAiTiA0NMKwMTMnMzYuMSJF!5e0!3m2!1sen!2ssa!4v1234567890123!5m2!1sen!2ssa"
                      width="100%"
                      height="100%"
                      style={{ border: 0 }}
                      allowFullScreen
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      title={language === 'ar' ? 'موقع محطات نور' : 'Noor Stations Location'}
                    ></iframe>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <Card className="border-0 shadow-2xl bg-white/80 backdrop-blur-sm contact-form-container">
              <CardHeader className="text-center pb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-noor-purple to-noor-orange rounded-full text-white mb-4 mx-auto">
                  <MessageCircle size={32} />
                </div>
                <CardTitle className="text-2xl font-bold bg-gradient-to-r from-noor-purple to-noor-orange bg-clip-text text-transparent">
                  {language === 'ar' ? 'أرسل لنا رسالة' : 'Send Us a Message'}
                </CardTitle>
                <p className="text-gray-600 mt-2">
                  {language === 'ar'
                    ? 'نحن هنا للاستماع إليك ومساعدتك في كل ما تحتاجه'
                    : 'We are here to listen to you and help you with everything you need'}
                </p>
              </CardHeader>
              <CardContent>
                {isSubmitted && (
                  <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    className="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl success-message"
                  >
                    <div className="flex items-center gap-3 text-green-700">
                      <CheckCircle size={24} />
                      <div>
                        <h4 className="font-semibold">
                          {language === 'ar' ? 'تم إرسال رسالتك بنجاح!' : 'Message sent successfully!'}
                        </h4>
                        <p className="text-sm">
                          {language === 'ar' ? 'سنتواصل معك خلال 24 ساعة' : 'We will contact you within 24 hours'}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                )}

                <form onSubmit={handleSubmit} className="space-y-8">
                  {/* Name Input */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                    className="relative group form-field-wrapper"
                  >
                    <div className="relative">
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="peer w-full px-4 py-4 text-lg bg-gray-50 border-2 border-gray-200 rounded-xl focus:border-noor-purple focus:bg-white transition-all duration-300 placeholder-transparent form-input"
                        placeholder={language === 'ar' ? 'الاسم' : 'Name'}
                      />
                      <Label
                        htmlFor="name"
                        className={`absolute transition-all duration-300 text-gray-500 px-2 bg-white
                          peer-placeholder-shown:top-4 peer-placeholder-shown:text-lg peer-placeholder-shown:text-gray-400 peer-placeholder-shown:bg-transparent
                          peer-focus:top-[-12px] peer-focus:text-sm peer-focus:text-noor-purple peer-focus:font-medium peer-focus:bg-white
                          ${formData.name ? 'top-[-12px] text-sm text-noor-purple font-medium bg-white' : 'top-4 text-lg bg-transparent'}
                          ${language === 'ar' ? 'left-3' : 'left-3'}`}
                      >
                        {language === 'ar' ? 'الاسم الكامل' : 'Full Name'}
                      </Label>
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-noor-purple to-noor-orange opacity-0 group-hover:opacity-10 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                  </motion.div>

                  {/* Email Input */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    className="relative group"
                  >
                    <div className="relative">
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="peer w-full px-4 py-4 text-lg bg-gray-50 border-2 border-gray-200 rounded-xl focus:border-noor-purple focus:bg-white transition-all duration-300 placeholder-transparent"
                        placeholder={language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                      />
                      <Label
                        htmlFor="email"
                        className={`absolute transition-all duration-300 text-gray-500 px-2 bg-white
                          peer-placeholder-shown:top-4 peer-placeholder-shown:text-lg peer-placeholder-shown:text-gray-400 peer-placeholder-shown:bg-transparent
                          peer-focus:top-[-12px] peer-focus:text-sm peer-focus:text-noor-purple peer-focus:font-medium peer-focus:bg-white
                          ${formData.email ? 'top-[-12px] text-sm text-noor-purple font-medium bg-white' : 'top-4 text-lg bg-transparent'}
                          ${language === 'ar' ? 'left-3' : 'left-3'}`}
                      >
                        {language === 'ar' ? 'البريد الإلكتروني' : 'Email Address'}
                      </Label>
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-noor-purple to-noor-orange opacity-0 group-hover:opacity-10 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                  </motion.div>

                  {/* Phone Input */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    className="relative group"
                  >
                    <div className="relative">
                      <div className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-500 font-medium z-10">
                        +966
                      </div>
                      <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => {
                          const digitsOnly = e.target.value.replace(/\D/g, '');
                          setFormData({ ...formData, phone: digitsOnly });
                        }}
                        required
                        maxLength={9}
                        className="peer w-full pl-20 pr-4 py-4 text-lg bg-gray-50 border-2 border-gray-200 rounded-xl focus:border-noor-purple focus:bg-white transition-all duration-300 placeholder-transparent"
                        placeholder={language === 'ar' ? 'رقم الهاتف' : 'Phone'}
                        dir="ltr"
                      />
                      <Label
                        htmlFor="phone"
                        className={`absolute transition-all duration-300 text-gray-500 px-2 bg-white
                          peer-placeholder-shown:top-4 peer-placeholder-shown:text-lg peer-placeholder-shown:text-gray-400 peer-placeholder-shown:bg-transparent
                          peer-focus:top-[-12px] peer-focus:text-sm peer-focus:text-noor-purple peer-focus:font-medium peer-focus:bg-white
                          ${formData.phone ? 'top-[-12px] text-sm text-noor-purple font-medium bg-white' : 'top-4 text-lg bg-transparent'}
                          ${language === 'ar' ? 'right-3' : 'left-16'}`}
                      >
                        {language === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
                      </Label>
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-noor-purple to-noor-orange opacity-0 group-hover:opacity-10 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                  </motion.div>


                  {/* Message Textarea */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className="relative group"
                  >
                    <div className="relative">
                      <Textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleChange}
                        required
                        rows={5}
                        minLength={10}
                        maxLength={500}
                        className="peer w-full px-4 py-4 text-lg bg-gray-50 border-2 border-gray-200 rounded-xl focus:border-noor-purple focus:bg-white transition-all duration-300 placeholder-transparent resize-none"
                        placeholder={language === 'ar' ? 'اكتب رسالتك هنا' : 'Write your message here'}
                      />
                      <div className={`absolute bottom-2 right-2 text-xs character-counter ${
                        formData.message.length > 450 ? 'danger' :
                        formData.message.length > 400 ? 'warning' : ''
                      }`}>
                        {formData.message.length}/500
                      </div>
                      <Label
                        htmlFor="message"
                        className={`absolute transition-all duration-300 text-gray-500 px-2 bg-white
                          peer-placeholder-shown:top-4 peer-placeholder-shown:text-lg peer-placeholder-shown:text-gray-400 peer-placeholder-shown:bg-transparent
                          peer-focus:top-[-12px] peer-focus:text-sm peer-focus:text-noor-purple peer-focus:font-medium peer-focus:bg-white
                          ${formData.message ? 'top-[-12px] text-sm text-noor-purple font-medium bg-white' : 'top-4 text-lg bg-transparent'}
                          ${language === 'ar' ? 'left-3' : 'left-3'}`}
                      >
                        {language === 'ar' ? 'اكتب رسالتك هنا' : 'Write your message here'}
                      </Label>
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-noor-purple to-noor-orange opacity-0 group-hover:opacity-10 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                  </motion.div>

                  {/* Submit Button */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full py-4 text-lg font-semibold bg-gradient-to-r from-noor-purple to-noor-orange text-white rounded-xl hover:shadow-lg hover:scale-[1.02] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed submit-button"
                    >
                      {isSubmitting ? (
                        <div className="flex items-center justify-center gap-3">
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>{language === 'ar' ? 'جاري الإرسال...' : 'Sending...'}</span>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center gap-3">
                          <Send size={20} />
                          <span>{language === 'ar' ? 'إرسال الرسالة' : 'Send Message'}</span>
                        </div>
                      )}
                    </Button>
                  </motion.div>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        </div>




      </main>

      {/* Compact Footer */}
      <footer className="bg-gradient-to-r from-noor-purple to-noor-orange text-white py-6 mt-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center md:text-left">
            <div>
              <h3 className="text-lg font-bold mb-2">
                {language === 'ar' ? 'محطات نور' : 'Noor Stations'}
              </h3>
              <p className="text-white/80 text-sm">
                {language === 'ar'
                  ? 'نقدم أفضل الخدمات لعملائنا الكرام'
                  : 'We provide the best services to our valued customers'
                }
              </p>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-2">
                {language === 'ar' ? 'تواصل معنا' : 'Contact Us'}
              </h3>
              <div className="space-y-1 text-white/80 text-sm">
                <p>+966 8003033313</p>
                <p><EMAIL></p>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-2">
                {language === 'ar' ? 'الموقع' : 'Location'}
              </h3>
              <p className="text-white/80 text-sm">
                {language === 'ar'
                  ? 'نجران، المملكة العربية السعودية'
                  : 'Najran, Saudi Arabia'
                }
              </p>
            </div>
          </div>
          <div className="border-t border-white/20 mt-4 pt-4 text-center">
            <p className="text-white/80 text-sm">
              {language === 'ar'
                ? '© 2025 محطات نور. جميع الحقوق محفوظة.'
                : '© 2025 Noor Stations. All rights reserved.'}
            </p>
          </div>
        </div>
      </footer>

      {/* Feedback Dialog */}
      <Dialog open={showFeedbackDialog} onOpenChange={setShowFeedbackDialog}>
        <DialogContent className="max-w-lg mx-auto">
          <DialogHeader>
            <DialogTitle className="text-center text-xl font-bold text-purple-700">
              {language === 'ar' ? 'شاركنا رأيك في الخدمة والبرنامج' : 'Share Your Feedback'}
            </DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            <FeedbackForm onSuccess={() => setShowFeedbackDialog(false)} />
          </div>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};

export default Contact;
