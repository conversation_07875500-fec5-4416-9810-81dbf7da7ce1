# تعليمات نهائية لحل مشكلة إضافة المناطق وتعديلها

## المشكلة

1. عند تمكين سياسات أمان الصفوف (RLS)، لا يمكن إضافة مناطق جديدة إلى جدول `cities`.
2. يجب أن يكون التعديل متاحًا للمالك فقط.

## الحلول المتاحة

### الحل 1: تطبيق سياسات أمان الصفوف الجديدة

هذا الحل يسمح للمستخدمين المسجلين بإضافة مناطق جديدة، وللمالك فقط بتعديل وحذف المناطق.

1. انتقل إلى لوحة تحكم Supabase: https://app.supabase.com/project/jtnqcyouncjoebqcalzh
2. انتقل إلى قسم "SQL Editor" من القائمة الجانبية.
3. انسخ محتوى الملف `supabase/migrations/20240601000005_owner_only_rls.sql` والصقه في محرر SQL.
4. انقر على زر "Run" لتنفيذ الاستعلام.

### الحل 2: تعطيل سياسات أمان الصفوف مؤقتًا

هذا الحل يعطل سياسات أمان الصفوف مؤقتًا، مما يسمح بإضافة وتعديل المناطق بدون قيود. استخدم هذا الحل فقط للاختبار.

1. انتقل إلى لوحة تحكم Supabase: https://app.supabase.com/project/jtnqcyouncjoebqcalzh
2. انتقل إلى قسم "SQL Editor" من القائمة الجانبية.
3. انسخ محتوى الملف `supabase/migrations/20240601000006_disable_rls.sql` والصقه في محرر SQL.
4. انقر على زر "Run" لتنفيذ الاستعلام.

بعد الانتهاء من الاختبار، يجب إعادة تمكين سياسات أمان الصفوف:

1. انتقل إلى لوحة تحكم Supabase: https://app.supabase.com/project/jtnqcyouncjoebqcalzh
2. انتقل إلى قسم "SQL Editor" من القائمة الجانبية.
3. انسخ محتوى الملف `supabase/migrations/20240601000007_enable_rls.sql` والصقه في محرر SQL.
4. انقر على زر "Run" لتنفيذ الاستعلام.

## استكشاف الأخطاء وإصلاحها

### 1. التحقق من دور المستخدم

تأكد من أن المستخدم مسجل الدخول بشكل صحيح. يمكنك التحقق من ذلك باستخدام الاستعلام التالي:

```sql
SELECT auth.uid(), auth.role();
```

### 2. التحقق من جدول admin_users

تأكد من أن المستخدم الذي يحاول تعديل المناطق موجود في جدول `admin_users` ولديه دور "owner". يمكنك التحقق من ذلك باستخدام الاستعلام التالي:

```sql
SELECT * FROM admin_users WHERE id = '<user_id>';
```

استبدل `<user_id>` بمعرف المستخدم الحالي.

### 3. التحقق من سياسات أمان الصفوف

تأكد من تطبيق سياسات أمان الصفوف بشكل صحيح. يمكنك التحقق من ذلك باستخدام الاستعلام التالي:

```sql
SELECT * FROM pg_policies WHERE tablename = 'cities';
```

### 4. التحقق من الأخطاء في وحدة التحكم

افتح وحدة التحكم في المتصفح (F12) وابحث عن أي أخطاء تظهر عند محاولة إضافة أو تعديل منطقة.

## ملاحظات هامة

1. سياسة "Allow authenticated to insert cities" تسمح لأي مستخدم مسجل الدخول بإضافة مدن جديدة.
2. سياسة "Allow owner to update cities" تسمح فقط للمستخدمين الذين لديهم دور "owner" في جدول `admin_users` بتعديل المدن.
3. سياسة "Allow owner to delete cities" تسمح فقط للمستخدمين الذين لديهم دور "owner" في جدول `admin_users` بحذف المدن.
4. تعطيل سياسات أمان الصفوف يجب أن يكون مؤقتًا فقط للاختبار، ويجب إعادة تمكينها في بيئة الإنتاج.
