/**
 * Performance Optimizer for Marker Rendering
 * محسن الأداء لعرض العلامات
 */

interface BatchProcessorOptions<T> {
  items: T[];
  batchSize: number;
  processor: (batch: T[]) => Promise<void> | void;
  onProgress?: (processed: number, total: number) => void;
  onComplete?: () => void;
  delay?: number;
}

class PerformanceOptimizer {
  private static instance: PerformanceOptimizer;
  private frameId: number | null = null;
  private isProcessing: boolean = false;

  static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  /**
   * Process items in optimized batches
   */
  async processBatch<T>(options: BatchProcessorOptions<T>): Promise<void> {
    const {
      items,
      batchSize,
      processor,
      onProgress,
      onComplete,
      delay = 0
    } = options;

    if (this.isProcessing) {
      console.warn('Batch processor is already running');
      return;
    }

    this.isProcessing = true;
    let processedCount = 0;

    try {
      for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        
        // Process batch
        await processor(batch);
        
        processedCount += batch.length;
        
        // Report progress
        if (onProgress) {
          onProgress(processedCount, items.length);
        }

        // Yield control to browser if more batches remain
        if (i + batchSize < items.length) {
          if (delay > 0) {
            await this.sleep(delay);
          } else {
            await this.nextFrame();
          }
        }
      }

      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      console.error('Error in batch processing:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Wait for next animation frame
   */
  private nextFrame(): Promise<void> {
    return new Promise(resolve => {
      this.frameId = requestAnimationFrame(() => resolve());
    });
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Cancel current processing
   */
  cancel(): void {
    if (this.frameId) {
      cancelAnimationFrame(this.frameId);
      this.frameId = null;
    }
    this.isProcessing = false;
  }

  /**
   * Check if currently processing
   */
  isRunning(): boolean {
    return this.isProcessing;
  }

  /**
   * Get optimal batch size based on device performance
   */
  getOptimalBatchSize(itemCount: number): number {
    // Detect device performance
    const isLowPerformance = this.detectLowPerformance();
    const isMobile = this.isMobileDevice();
    
    // Base batch sizes
    let batchSize: number;
    
    if (isLowPerformance) {
      batchSize = 5;
    } else if (isMobile) {
      batchSize = 15;
    } else {
      batchSize = 25;
    }

    // Adjust based on total item count
    if (itemCount > 1000) {
      batchSize = Math.max(batchSize - 5, 5);
    } else if (itemCount < 50) {
      batchSize = Math.min(batchSize + 10, itemCount);
    }

    return batchSize;
  }

  /**
   * Detect low performance device
   */
  private detectLowPerformance(): boolean {
    const isSlowCPU = navigator.hardwareConcurrency !== undefined && navigator.hardwareConcurrency <= 2;
    const isOldDevice = navigator.userAgent.includes('Android 4') || navigator.userAgent.includes('iPhone OS 9');
    const hasLowMemory = (navigator as any).deviceMemory !== undefined && (navigator as any).deviceMemory <= 2;
    
    return isSlowCPU || isOldDevice || hasLowMemory;
  }

  /**
   * Detect mobile device
   */
  private isMobileDevice(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  /**
   * Measure performance of a function
   */
  async measurePerformance<T>(
    name: string,
    fn: () => Promise<T> | T
  ): Promise<{ result: T; duration: number }> {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    
    console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`);
    
    return { result, duration };
  }

  /**
   * Throttle function execution
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout | null = null;
    let lastExecTime = 0;

    return (...args: Parameters<T>) => {
      const currentTime = Date.now();

      if (currentTime - lastExecTime > delay) {
        func(...args);
        lastExecTime = currentTime;
      } else {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => {
          func(...args);
          lastExecTime = Date.now();
        }, delay - (currentTime - lastExecTime));
      }
    };
  }

  /**
   * Debounce function execution
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout | null = null;

    return (...args: Parameters<T>) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => func(...args), delay);
    };
  }

  /**
   * Create optimized event listener with passive option
   */
  addOptimizedEventListener(
    element: Element,
    event: string,
    handler: EventListener,
    options?: AddEventListenerOptions
  ): () => void {
    const optimizedOptions = {
      passive: true,
      capture: false,
      ...options
    };

    element.addEventListener(event, handler, optimizedOptions);

    // Return cleanup function
    return () => {
      element.removeEventListener(event, handler, optimizedOptions);
    };
  }

  /**
   * Optimize DOM operations by batching them
   */
  batchDOMOperations(operations: (() => void)[]): void {
    // Use DocumentFragment for better performance
    const fragment = document.createDocumentFragment();
    
    // Batch all DOM operations
    requestAnimationFrame(() => {
      operations.forEach(operation => operation());
    });
  }

  /**
   * Check if browser supports modern features
   */
  getFeatureSupport(): {
    webGL: boolean;
    webWorkers: boolean;
    intersectionObserver: boolean;
    requestIdleCallback: boolean;
  } {
    return {
      webGL: !!window.WebGLRenderingContext,
      webWorkers: !!window.Worker,
      intersectionObserver: !!window.IntersectionObserver,
      requestIdleCallback: !!(window as any).requestIdleCallback
    };
  }

  /**
   * Execute function when browser is idle
   */
  executeWhenIdle(callback: () => void, timeout: number = 5000): void {
    if ((window as any).requestIdleCallback) {
      (window as any).requestIdleCallback(callback, { timeout });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(callback, 0);
    }
  }
}

// Export singleton instance
export const performanceOptimizer = PerformanceOptimizer.getInstance();
export default performanceOptimizer;
