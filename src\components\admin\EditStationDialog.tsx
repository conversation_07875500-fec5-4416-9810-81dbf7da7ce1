
import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { GasStation } from "@/types/station";

interface EditStationDialogProps {
  isOpen: boolean;
  station: Partial<GasStation>;
  onClose: () => void;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onSubmit: () => void;
}

const EditStationDialog = ({
  isOpen,
  station,
  onClose,
  onInputChange,
  onSubmit,
}: EditStationDialogProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="rtl max-w-xl">
        <DialogHeader>
          <DialogTitle>تعديل المحطة</DialogTitle>
        </DialogHeader>
        {/* Add your form fields here */}
        <form onSubmit={e => { e.preventDefault(); onSubmit(); }}>
          <div className="mb-4">
            <Label htmlFor="name">اسم المحطة</Label>
            <Input id="name" name="name" value={station.name || ''} onChange={onInputChange} />
          </div>
          {/* Add more fields as needed */}
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>إلغاء</Button>
            <Button type="submit">حفظ التغييرات</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditStationDialog;