import React, { useState, useEffect } from 'react';
import { cn } from "@/lib/utils";

interface LoadingIndicatorProps {
  delay?: number; // تأخير قبل إظهار المؤشر (بالمللي ثانية)
  minDisplay?: number; // الحد الأدنى لوقت العرض (بالمللي ثانية)
  className?: string; // تخصيص الشكل
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  delay = 300,
  minDisplay = 500,
  className
}) => {
  const [visible, setVisible] = useState(false);
  const [removing, setRemoving] = useState(false);

  useEffect(() => {
    // تأخير إظهار المؤشر لتجنب الوميض السريع
    const showTimer = setTimeout(() => {
      setVisible(true);
    }, delay);

    return () => {
      clearTimeout(showTimer);
    };
  }, [delay]);

  // إذا لم يكن مرئياً، لا تعرض شيئاً
  if (!visible) return null;

  return (
    <div 
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm transition-opacity",
        removing ? "opacity-0" : "opacity-100",
        className
      )}
    >
      <div className="animate-spin">
        <svg
          className="w-10 h-10 text-primary"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      </div>
    </div>
  );
};

export default LoadingIndicator;
