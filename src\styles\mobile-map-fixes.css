/**
 * Mobile Map Fixes
 * إصلاحات خاصة بالخريطة على الأجهزة المحمولة
 */

/* تحسينات عامة للخريطة على الأجهزة المحمولة */
@media (max-width: 768px) {
  /* تحسين العلامات على الأجهزة المحمولة */
  .mapboxgl-marker {
    transform-origin: center center !important;
    transition: transform 0.2s ease !important;
  }
  
  /* تحسين العلامات العادية */
  .marker-pin {
    min-width: 36px !important;
    min-height: 36px !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: rgba(0,0,0,0.1) !important;
    cursor: pointer !important;
    z-index: 100 !important;
  }
  
  /* تحسين العلامات المحددة */
  .marker-pin.selected {
    min-width: 44px !important;
    min-height: 44px !important;
    z-index: 1000 !important;
    transform: scale(1.1) !important;
  }
  
  /* تحسين النوافذ المنبثقة */
  .mapboxgl-popup {
    max-width: 90vw !important;
    z-index: 2000 !important;
  }
  
  .mapboxgl-popup-content {
    padding: 15px !important;
    font-size: 14px !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
  }
  
  .mapboxgl-popup-close-button {
    font-size: 18px !important;
    padding: 8px !important;
    right: 8px !important;
    top: 8px !important;
  }
  
  /* تحسين أزرار التحكم في الخريطة */
  .mapboxgl-ctrl-group {
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  }
  
  .mapboxgl-ctrl-group button {
    width: 40px !important;
    height: 40px !important;
    font-size: 16px !important;
  }
  
  /* تحسين شريط التكبير */
  .mapboxgl-ctrl-zoom-in,
  .mapboxgl-ctrl-zoom-out {
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent !important;
  }
  
  /* تحسين البوصلة */
  .mapboxgl-ctrl-compass {
    touch-action: manipulation !important;
  }
}

/* تحسينات خاصة للشاشات الصغيرة جداً */
@media (max-width: 480px) {
  /* العلامات أكبر قليلاً على الشاشات الصغيرة */
  .marker-pin {
    min-width: 40px !important;
    min-height: 40px !important;
  }
  
  .marker-pin.selected {
    min-width: 48px !important;
    min-height: 48px !important;
  }
  
  /* النوافذ المنبثقة أكبر */
  .mapboxgl-popup-content {
    padding: 20px !important;
    font-size: 16px !important;
  }
  
  /* أزرار التحكم أكبر */
  .mapboxgl-ctrl-group button {
    width: 44px !important;
    height: 44px !important;
    font-size: 18px !important;
  }
}

/* إصلاحات خاصة بـ iOS */
@supports (-webkit-overflow-scrolling: touch) {
  .mapboxgl-map {
    -webkit-overflow-scrolling: touch !important;
    -webkit-transform: translateZ(0) !important;
  }
  
  .marker-pin {
    -webkit-transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
  }
  
  .mapboxgl-popup {
    -webkit-transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
  }
}

/* إصلاحات خاصة بـ Android */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .marker-pin {
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
  }
}

/* تحسينات للأداء على الأجهزة المحمولة */
@media (max-width: 768px) {
  /* تقليل التأثيرات البصرية للأداء */
  .mapboxgl-map {
    will-change: transform !important;
  }
  
  .marker-pin {
    will-change: transform !important;
    backface-visibility: hidden !important;
  }
  
  /* تحسين الحركة */
  .mapboxgl-popup {
    transition: opacity 0.2s ease !important;
  }
  
  /* إخفاء العناصر غير الضرورية على الشاشات الصغيرة */
  .mapboxgl-ctrl-attrib {
    display: none !important;
  }
}

/* تحسينات للمس */
@media (pointer: coarse) {
  .marker-pin {
    cursor: pointer !important;
    touch-action: manipulation !important;
  }
  
  .mapboxgl-ctrl-group button {
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent !important;
  }
}

/* تحسينات للوضع الأفقي على الأجهزة المحمولة */
@media (max-width: 768px) and (orientation: landscape) {
  .mapboxgl-popup {
    max-width: 50vw !important;
  }
  
  .mapboxgl-popup-content {
    max-height: 60vh !important;
    overflow-y: auto !important;
  }
}

/* إصلاحات للتفاعل باللمس */
.marker-pin {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-touch-callout: none !important;
}

/* تحسين الرؤية على الشاشات عالية الكثافة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .marker-pin {
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
  }
}
