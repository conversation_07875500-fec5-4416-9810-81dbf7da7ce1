/**
 * Smart Protection Styles
 * حماية ذكية تسمح بأدوات المطور والنقر الأيمن مع حماية المحتوى الحساس
 */

/* السماح بتحديد النص في جميع العناصر */
* {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* حماية العناصر الحساسة فقط */
.code-protected,
script,
style,
.sensitive-data {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  pointer-events: none;
}

/* تحسين تجربة النقر الأيمن */
.context-menu-allowed {
  -webkit-touch-callout: default;
  -webkit-tap-highlight-color: rgba(0,0,0,0.1);
}

/* تحسين تجربة التحديد */
.text-selectable {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
  cursor: text;
}

/* تحسين تجربة السحب والإفلات */
.draggable {
  -webkit-user-drag: auto;
  -khtml-user-drag: auto;
  -moz-user-drag: auto;
  -o-user-drag: auto;
  user-drag: auto;
  cursor: grab;
}

.draggable:active {
  cursor: grabbing;
}

/* منع سحب العناصر الحساسة فقط */
.no-drag {
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
}

/* تحسين تجربة الطباعة */
@media print {
  .code-protected,
  .sensitive-data {
    display: none !important;
  }
  
  .print-hidden {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body::after {
    content: "بعض المحتوى محمي ولا يظهر في الطباعة لأسباب أمنية";
    display: block;
    text-align: center;
    font-size: 12px;
    color: #666;
    margin-top: 20px;
    page-break-inside: avoid;
  }
}

/* تحسين إمكانية الوصول */
.accessible-text {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
  outline: none;
}

.accessible-text:focus {
  outline: 2px solid #007AFF;
  outline-offset: 2px;
}

/* تحسين تجربة اللمس */
.touch-friendly {
  -webkit-tap-highlight-color: rgba(0,0,0,0.1);
  -webkit-touch-callout: default;
  touch-action: manipulation;
}

/* تحسين تجربة الكيبورد */
.keyboard-navigable {
  outline: none;
}

.keyboard-navigable:focus {
  outline: 2px solid #007AFF;
  outline-offset: 2px;
}

/* تحسين تجربة الماوس */
.mouse-friendly {
  cursor: pointer;
}

.mouse-friendly:hover {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

/* تحسين تجربة النماذج */
input,
textarea,
select {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
  -webkit-touch-callout: default;
}

/* تحسين تجربة الروابط */
a {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  -webkit-touch-callout: default;
  -webkit-tap-highlight-color: rgba(0,0,0,0.1);
}

/* تحسين تجربة الأزرار */
button {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  cursor: pointer;
}

/* تحسين تجربة الصور */
img {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  -webkit-user-drag: auto;
  -khtml-user-drag: auto;
  -moz-user-drag: auto;
  -o-user-drag: auto;
  user-drag: auto;
}

img.protected {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
}

/* تحسين تجربة الجداول */
table,
th,
td {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* تحسين تجربة القوائم */
ul,
ol,
li {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* تحسين تجربة النصوص */
p,
span,
div,
h1, h2, h3, h4, h5, h6 {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* رسالة ترحيب للمطورين */
.developer-welcome {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  font-size: 12px;
  z-index: 1000;
  opacity: 0;
  animation: fadeInOut 5s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  20%, 80% { opacity: 1; }
}

/* تحسين تجربة الخريطة */
.mapboxgl-map {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

/* تحسين تجربة المحتوى التفاعلي */
.interactive-content {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  -webkit-touch-callout: default;
}
