# Headers for Cloudflare Pages
/*
  X-Frame-Options: SAMEORIGIN
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: geolocation=(self), microphone=(), camera=()
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: https://cdn.gpteng.co https://maps.googleapis.com https://maps.gstatic.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https: https://maps.googleapis.com https://maps.gstatic.com https://*.googleusercontent.com; connect-src 'self' https://*.supabase.co https://*.mapbox.com https://*.kaspersky-labs.com http://*.kaspersky-labs.com https://maps.googleapis.com; worker-src 'self' blob:; frame-src 'self' https://www.google.com https://maps.google.com;

# CSS files - IMPORTANT: Fix MIME type
/assets/*.css
  Cache-Control: public, max-age=********, immutable
  Content-Type: text/css

# JavaScript files
/assets/*.js
  Cache-Control: public, max-age=********, immutable
  Content-Type: application/javascript

# Cache static assets
/assets/*
  Cache-Control: public, max-age=********, immutable

# Cache images
*.png
  Cache-Control: public, max-age=********, immutable
  Content-Type: image/png

*.jpg
  Cache-Control: public, max-age=********, immutable
  Content-Type: image/jpeg

*.ico
  Cache-Control: public, max-age=********, immutable
  Content-Type: image/x-icon

# HTML files - no cache for SPA routing
*.html
  Cache-Control: no-cache, no-store, must-revalidate
  Content-Type: text/html; charset=utf-8

# Root index.html
/
  Cache-Control: no-cache, no-store, must-revalidate
  Content-Type: text/html; charset=utf-8
