# Headers for Cloudflare Pages
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: geolocation=(), microphone=(), camera=()

# CSS files - IMPORTANT: Fix MIME type
/assets/*.css
  Cache-Control: public, max-age=********, immutable
  Content-Type: text/css

# JavaScript files
/assets/*.js
  Cache-Control: public, max-age=********, immutable
  Content-Type: application/javascript

# Cache static assets
/assets/*
  Cache-Control: public, max-age=********, immutable

# Cache images
*.png
  Cache-Control: public, max-age=********, immutable
  Content-Type: image/png

*.jpg
  Cache-Control: public, max-age=********, immutable
  Content-Type: image/jpeg

*.ico
  Cache-Control: public, max-age=********, immutable
  Content-Type: image/x-icon

# HTML files - no cache for SPA routing
*.html
  Cache-Control: no-cache, no-store, must-revalidate
  Content-Type: text/html; charset=utf-8

# Root index.html
/
  Cache-Control: no-cache, no-store, must-revalidate
  Content-Type: text/html; charset=utf-8
