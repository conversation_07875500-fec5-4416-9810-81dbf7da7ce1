// توفير متغير process في المتصفح - إصدار محسن لـ Cloudflare
(function() {
  'use strict';

  // تحديد البيئة بناءً على الموقع
  const isProduction = window.location.hostname !== 'localhost' &&
                      window.location.hostname !== '127.0.0.1' &&
                      !window.location.hostname.includes('dev') &&
                      !window.location.hostname.includes('127.0.0.1');

  // إنشاء كائن process كامل
  window.process = window.process || {
    env: {
      NODE_ENV: isProduction ? 'production' : 'development'
    },
    browser: true,
    version: '18.0.0',
    versions: {
      node: '18.0.0'
    },
    platform: 'browser',
    arch: 'x64',
    pid: 1,
    title: 'browser',
    argv: [],
    execPath: '',
    cwd: function() { return '/'; },
    chdir: function() {},
    umask: function() { return 0; },
    nextTick: function(fn) { setTimeout(fn, 0); },
    exit: function() {},
    kill: function() {},
    hrtime: function() { return [0, 0]; }
  };

  // إضافة global للتوافق مع React
  if (typeof window.global === 'undefined') {
    window.global = window;
  }

  // إضافة globalThis للتوافق
  if (typeof window.globalThis === 'undefined') {
    window.globalThis = window;
  }

  // إضافة Buffer للتوافق
  if (typeof window.Buffer === 'undefined') {
    window.Buffer = {
      isBuffer: function() { return false; },
      from: function(data) { return data; },
      alloc: function(size) { return new Array(size).fill(0); }
    };
  }

  // إضافة require mock للتوافق
  if (typeof window.require === 'undefined') {
    window.require = function(module) {
      console.warn('require() is not available in browser environment:', module);
      return {};
    };
  }

  // إضافة module mock للتوافق
  if (typeof window.module === 'undefined') {
    window.module = {
      exports: {}
    };
  }

  // إضافة exports mock للتوافق
  if (typeof window.exports === 'undefined') {
    window.exports = window.module.exports;
  }

  console.log('Process shim loaded successfully. Environment:', window.process.env.NODE_ENV);
})();
