// توفير متغير process في المتصفح
(function() {
  'use strict';

  // التحقق من وجود process مسبقاً
  if (typeof window.process !== 'undefined') {
    return;
  }

  // تحديد البيئة بناءً على الموقع
  const isProduction = window.location.hostname !== 'localhost' &&
                      window.location.hostname !== '127.0.0.1' &&
                      !window.location.hostname.includes('dev');

  // إنشاء كائن process محدود
  window.process = {
    env: {
      NODE_ENV: isProduction ? 'production' : 'development'
    },
    browser: true,
    version: '',
    versions: {
      node: ''
    }
  };

  // إضافة global للتوافق مع React
  if (typeof window.global === 'undefined') {
    window.global = window;
  }

  // إضافة Buffer للتوافق
  if (typeof window.Buffer === 'undefined') {
    window.Buffer = {};
  }

  // منع تعديل الكائن
  Object.freeze(window.process);
  Object.freeze(window.process.env);
})();
