// توفير متغير process في المتصفح - مبسط للتوافق مع Cloudflare
(function() {
  'use strict';

  // تحديد البيئة
  const isProduction = window.location.hostname !== 'localhost' &&
                      window.location.hostname !== '127.0.0.1';

  // إنشاء كائن process مبسط
  if (typeof window.process === 'undefined') {
    window.process = {
      env: {
        NODE_ENV: isProduction ? 'production' : 'development'
      },
      browser: true
    };
  }

  // إضافة global
  if (typeof window.global === 'undefined') {
    window.global = window;
  }

  console.log('Process shim loaded. Environment:', window.process.env.NODE_ENV);
})();
