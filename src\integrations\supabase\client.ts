// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';
import logger from '@/utils/logger';

// استخدام import.meta.env بدلاً من process.env
const SUPABASE_URL = "https://jtnqcyouncjoebqcalzh.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0bnFjeW91bmNqb2VicWNhbHpoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU1MTkyMTIsImV4cCI6MjA2MTA5NTIxMn0.VWK5DvW4LxFDLZ-RYaQXDABUaPM8y2vGFnXgKwGZ9Dk";
// Service role key is removed as it's causing authentication issues

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// تحديد الموقع الحالي للتطبيق
const getLocalStorageKey = () => {
  // في بيئة الإنتاج، استخدم المجال الفعلي
  if (typeof window !== 'undefined' && window.location.hostname !== 'localhost') {
    return `sb-${SUPABASE_URL.replace(/^https?:\/\//, '').replace(/\/$/, '')}-auth-token`;
  }
  // في بيئة التطوير، استخدم مفتاح محدد
  return `sb-${SUPABASE_URL.replace(/^https?:\/\//, '').replace(/\/$/, '')}-auth-token-dev`;
};

// إنشاء عميل Supabase مع إعدادات محسنة للجلسة
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// إنشاء عميل خاص للعمليات المعقدة مثل إضافة المحطات
export const adminSupabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  global: {
    headers: {
      'X-Client-Info': 'station-noor-admin',
      'Prefer': 'return=representation'
    }
  }
});

// دالة خاصة لإضافة محطة تتجاوز مشكلة RLS
export const insertStationDirect = async (stationData: any) => {
  try {
    console.log("Trying insertStationDirect with simplified approach");

    // Create a minimal data object with only required fields
    const minimalData = {
      name: stationData.name,
      region: stationData.region || '',
      latitude: stationData.latitude,
      longitude: stationData.longitude,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log("Attempting direct insert with minimal data:", minimalData);

    // Try direct insert first
    const { data, error } = await supabase
      .from('stations')
      .insert([minimalData])
      .select()
      .single();

    if (error) {
      console.error("Direct insert failed:", error);

      // If direct insert fails, try RPC
      console.log("Falling back to RPC insert");
      const { data: rpcData, error: rpcError } = await supabase
        .rpc('insert_station', { station_data: minimalData });

      if (rpcError) {
        console.error("RPC insert failed:", rpcError);
        throw new Error(`Failed to insert station: ${rpcError.message}`);
      }

      if (!rpcData) {
        throw new Error("RPC insert succeeded but no data was returned");
      }

      console.log("RPC insert succeeded:", rpcData);
      return rpcData;
    }

    if (!data) {
      throw new Error("Direct insert succeeded but no data was returned");
    }

    console.log("Direct insert succeeded:", data);

    // If we have additional data, update the record
    if (data.id && (stationData.sub_region || stationData.fuel_types || stationData.additional_info)) {
      try {
        console.log("Updating with additional data");
        const updateData: any = {
          updated_at: new Date().toISOString()
        };
        if (stationData.sub_region) updateData.sub_region = stationData.sub_region;
        if (stationData.fuel_types) updateData.fuel_types = stationData.fuel_types;
        if (stationData.additional_info) updateData.additional_info = stationData.additional_info;

        const { data: updatedData, error: updateError } = await supabase
          .from('stations')
          .update(updateData)
          .eq('id', data.id)
          .select()
          .single();

        if (updateError) {
          console.error("Failed to update with additional data:", updateError);
          return data; // Return original data even if update fails
        }

        console.log("Update succeeded:", updatedData);
        return updatedData;
      } catch (updateError) {
        console.error("Error updating additional data:", updateError);
        return data; // Return original data if update fails
      }
    }

    return data;
  } catch (error) {
    console.error("Error in insertStationDirect:", error);
    throw error;
  }
};

/**
 * إصلاح دالة تحديث المحطة
 * يقوم بإنشاء الدالة بالمعلمات الصحيحة
 */
export const fixUpdateStationFunction = async (): Promise<boolean> => {
  try {
    logger.debug('Attempting to fix update_station function...');

    // SQL لإصلاح دالة تحديث المحطة
    const fixSQL = `
      -- Drop the existing function if it exists
      DROP FUNCTION IF EXISTS public.update_station(UUID, JSONB);

      -- Create the function with the correct parameter names
      CREATE OR REPLACE FUNCTION public.update_station(
          p_station_id UUID,
          p_station_data JSONB
      ) RETURNS SETOF stations AS $$
      BEGIN
          RETURN QUERY
          UPDATE public.stations SET
              name = COALESCE(p_station_data->>'name', name),
              region = COALESCE(p_station_data->>'region', region),
              sub_region = COALESCE(p_station_data->>'sub_region', sub_region),
              latitude = COALESCE((p_station_data->>'latitude')::numeric, latitude),
              longitude = COALESCE((p_station_data->>'longitude')::numeric, longitude),
              fuel_types = COALESCE(p_station_data->>'fuel_types', fuel_types),
              additional_info = COALESCE(p_station_data->>'additional_info', additional_info),
              updated_at = now()
          WHERE id = p_station_id
          RETURNING *;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;

      -- Grant execute permission to authenticated users
      GRANT EXECUTE ON FUNCTION public.update_station(UUID, JSONB) TO authenticated;
    `;

    // تنفيذ الكود SQL - محاولة أولى باستخدام المعلمة الصحيحة
    const { error } = await supabase.rpc('execute_sql', { query: fixSQL });

    if (error) {
      logger.warn('Failed to fix update_station function using execute_sql:', error);

      // محاولة ثانية: استخدام طريقة بديلة - تحديث الدالة مباشرة
      try {
        logger.debug('Attempting alternative method to fix update_station function');

        // حذف الدالة الموجودة
        await supabase.rpc('direct_update_station_function');

        return true;
      } catch (altError) {
        logger.error('Failed to fix update_station function using alternative method:', altError);
        return false;
      }
    }

    logger.debug('Successfully fixed update_station function');
    return true;
  } catch (error) {
    logger.error('Error fixing update_station function:', error);
    return false;
  }
};

// تنفيذ إصلاح دالة تحديث المحطة عند استيراد هذا الملف
// سيتم تنفيذ هذا الكود مرة واحدة عند بدء التطبيق
(async () => {
  try {
    // تأخير قصير للتأكد من أن التطبيق قد بدأ بشكل كامل
    setTimeout(async () => {
      // التحقق من حالة تسجيل الدخول قبل محاولة الإصلاح
      const { data } = await supabase.auth.getSession();
      if (data.session) {
        // المستخدم مسجل الدخول، يمكننا تنفيذ الإصلاح
        await fixUpdateStationFunction();
      } else {
        // سنحاول الإصلاح عند تسجيل الدخول
        logger.debug('User not logged in, will attempt to fix update_station function after login');

        // الاستماع لحدث تسجيل الدخول
        supabase.auth.onAuthStateChange((event) => {
          if (event === 'SIGNED_IN') {
            fixUpdateStationFunction();
          }
        });
      }
    }, 2000);
  } catch (error) {
    logger.error('Error in auto-fix initialization:', error);
  }
})();
