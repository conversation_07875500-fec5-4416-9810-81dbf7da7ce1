# Cloudflare Pages configuration
name = "web-craft-arabia-pro"
compatibility_date = "2024-01-01"

[build]
command = "npm install && npm run build"
cwd = "."
publish = "dist"

[build.environment_variables]
NODE_VERSION = "18"
NPM_FLAGS = "--include=dev"

# Environment variables for production
[env.production]
NODE_ENV = "production"

# Environment variables for preview
[env.preview]
NODE_ENV = "development"
