@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 210 40% 20%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --ring: 216 34% 17%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .map-container {
    width: 100%;
    transition: height 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
  }

  /* تعديلات إضافية للخريطة على الشاشات الصغيرة */
  @media (max-width: 480px) {
    /* الخريطة تكون دائمًا بالحجم الكامل على الأجهزة المحمولة */
    .map-container {
      min-height: 300px;
      max-height: 300px;
    }

    /* لا نحتاج إلى هذه الحالة بعد الآن لأن الخريطة ستكون دائمًا موسعة */
    .map-container:not(.expanded) {
      min-height: 300px;
      max-height: 300px;
    }

    .map-container.expanded {
      min-height: 300px;
      max-height: 300px;
    }
  }

  /* تحريك الزر عند توسيع الخريطة */
  .map-expand-button {
    transition: all 0.3s ease-in-out;
  }

  /* نبض للإشارة إلى قابلية التوسيع */
  @keyframes pulse-expand {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.data-[state=closed]:animate-fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.toast-welcome {
  background-color: #f5f3ff;
  border-left: 4px solid #8b5cf6;
}
