/**
 * Virtual Marker Renderer for High Performance
 * عارض العلامات الافتراضي للأداء العالي
 */

import mapboxgl from 'mapbox-gl';
import { GasStation } from '@/types/station';
import { markerPool } from './markerPool';

interface VirtualMarkerOptions {
  map: mapboxgl.Map;
  stations: GasStation[];
  selectedStation: GasStation | null;
  onSelectStation: (station: GasStation) => void;
  createPopupContent: (station: GasStation) => HTMLElement;
}

interface RenderedMarker {
  pooledMarker: any;
  station: GasStation;
  popup?: mapboxgl.Popup;
}

class VirtualMarkerRenderer {
  private map: mapboxgl.Map;
  private stations: GasStation[] = [];
  private selectedStation: GasStation | null = null;
  private onSelectStation: (station: GasStation) => void;
  private createPopupContent: (station: GasStation) => HTMLElement;
  
  private renderedMarkers: Map<string, RenderedMarker> = new Map();
  private visibleBounds: mapboxgl.LngLatBounds | null = null;
  private renderTimeout: NodeJS.Timeout | null = null;
  private isRendering: boolean = false;
  
  // Performance settings
  private maxMarkersPerFrame: number = 20;
  private renderDelay: number = 16; // ~60fps
  private viewportPadding: number = 0.01; // Padding around viewport
  
  constructor(options: VirtualMarkerOptions) {
    this.map = options.map;
    this.stations = options.stations;
    this.selectedStation = options.selectedStation;
    this.onSelectStation = options.onSelectStation;
    this.createPopupContent = options.createPopupContent;
    
    this.setupEventListeners();
  }

  /**
   * Setup map event listeners
   */
  private setupEventListeners(): void {
    // Throttled render on map move
    this.map.on('moveend', () => this.scheduleRender());
    this.map.on('zoomend', () => this.scheduleRender());
    this.map.on('resize', () => this.scheduleRender());
    
    // Immediate render on data change
    this.map.on('sourcedata', () => this.scheduleRender());
  }

  /**
   * Schedule render with debouncing
   */
  private scheduleRender(): void {
    if (this.renderTimeout) {
      clearTimeout(this.renderTimeout);
    }
    
    this.renderTimeout = setTimeout(() => {
      this.render();
    }, this.renderDelay);
  }

  /**
   * Update stations data
   */
  updateStations(stations: GasStation[]): void {
    this.stations = stations;
    this.scheduleRender();
  }

  /**
   * Update selected station
   */
  updateSelectedStation(station: GasStation | null): void {
    const previousSelected = this.selectedStation;
    this.selectedStation = station;
    
    // Update only affected markers
    if (previousSelected) {
      this.updateMarkerSelection(previousSelected, false);
    }
    if (station) {
      this.updateMarkerSelection(station, true);
    }
  }

  /**
   * Update marker selection state
   */
  private updateMarkerSelection(station: GasStation, isSelected: boolean): void {
    const rendered = this.renderedMarkers.get(station.id);
    if (rendered) {
      // Update marker appearance
      const element = rendered.pooledMarker.element;
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      
      if (isSelected) {
        element.className = 'marker-pin selected mapboxgl-marker mapboxgl-marker-anchor-center';
        element.style.width = isMobile ? '48px' : '40px';
        element.style.height = isMobile ? '48px' : '40px';
        element.style.zIndex = '1000';
        element.style.transform = 'scale(1.2)';
      } else {
        element.className = 'marker-pin';
        element.style.width = isMobile ? '36px' : '28px';
        element.style.height = isMobile ? '36px' : '28px';
        element.style.zIndex = '1';
        element.style.transform = 'scale(1)';
      }
    }
  }

  /**
   * Main render function
   */
  private async render(): Promise<void> {
    if (this.isRendering) return;
    this.isRendering = true;

    try {
      // Get current viewport bounds
      const bounds = this.map.getBounds();
      const expandedBounds = new mapboxgl.LngLatBounds(
        [bounds.getWest() - this.viewportPadding, bounds.getSouth() - this.viewportPadding],
        [bounds.getEast() + this.viewportPadding, bounds.getNorth() + this.viewportPadding]
      );

      // Find stations in viewport
      const visibleStations = this.stations.filter(station =>
        expandedBounds.contains([station.longitude, station.latitude])
      );

      // Remove markers outside viewport
      await this.removeInvisibleMarkers(expandedBounds);

      // Add new markers in batches
      await this.addVisibleMarkers(visibleStations);

      this.visibleBounds = expandedBounds;
    } catch (error) {
      console.error('Error in virtual marker render:', error);
    } finally {
      this.isRendering = false;
    }
  }

  /**
   * Remove markers outside viewport
   */
  private async removeInvisibleMarkers(bounds: mapboxgl.LngLatBounds): Promise<void> {
    const toRemove: string[] = [];
    
    this.renderedMarkers.forEach((rendered, stationId) => {
      const station = rendered.station;
      if (!bounds.contains([station.longitude, station.latitude])) {
        toRemove.push(stationId);
      }
    });

    // Remove in batches
    for (let i = 0; i < toRemove.length; i += this.maxMarkersPerFrame) {
      const batch = toRemove.slice(i, i + this.maxMarkersPerFrame);
      
      batch.forEach(stationId => {
        const rendered = this.renderedMarkers.get(stationId);
        if (rendered) {
          // Remove popup if exists
          if (rendered.popup) {
            rendered.popup.remove();
          }
          
          // Return marker to pool
          markerPool.returnMarker(rendered.pooledMarker);
          
          // Remove from rendered map
          this.renderedMarkers.delete(stationId);
        }
      });

      // Yield control to browser
      if (i + this.maxMarkersPerFrame < toRemove.length) {
        await new Promise(resolve => requestAnimationFrame(resolve));
      }
    }
  }

  /**
   * Add visible markers in batches
   */
  private async addVisibleMarkers(visibleStations: GasStation[]): Promise<void> {
    const toAdd = visibleStations.filter(station => 
      !this.renderedMarkers.has(station.id)
    );

    // Add in batches
    for (let i = 0; i < toAdd.length; i += this.maxMarkersPerFrame) {
      const batch = toAdd.slice(i, i + this.maxMarkersPerFrame);
      
      batch.forEach(station => {
        this.addMarker(station);
      });

      // Yield control to browser
      if (i + this.maxMarkersPerFrame < toAdd.length) {
        await new Promise(resolve => requestAnimationFrame(resolve));
      }
    }
  }

  /**
   * Add single marker
   */
  private addMarker(station: GasStation): void {
    try {
      // Validate coordinates before creating marker
      if (!station.longitude || !station.latitude ||
          isNaN(station.longitude) || isNaN(station.latitude) ||
          station.longitude < -180 || station.longitude > 180 ||
          station.latitude < -90 || station.latitude > 90) {
        console.warn(`Invalid coordinates for station ${station.id}: lat=${station.latitude}, lng=${station.longitude}`);
        return;
      }

      console.log(`Virtual renderer creating marker for station ${station.id} at [${station.longitude}, ${station.latitude}]`);

      const isSelected = this.selectedStation?.id === station.id;
      const pooledMarker = markerPool.getMarker(station, isSelected);

      // Add click event
      pooledMarker.element.addEventListener('click', () => {
        this.onSelectStation(station);
      });

      // Add to map
      pooledMarker.marker.addTo(this.map);

      // Create popup
      const popup = new mapboxgl.Popup({
        closeButton: true,
        closeOnClick: false,
        offset: 25,
        className: 'station-popup',
        maxWidth: '300px',
        focusAfterOpen: false,
      });

      popup.setDOMContent(this.createPopupContent(station));

      // Add hover events
      this.addHoverEvents(pooledMarker.element, popup);

      // Store rendered marker
      this.renderedMarkers.set(station.id, {
        pooledMarker,
        station,
        popup
      });

    } catch (error) {
      console.error(`Error adding marker for station ${station.id}:`, error);
    }
  }

  /**
   * Add hover events to marker
   */
  private addHoverEvents(element: HTMLElement, popup: mapboxgl.Popup): void {
    let hoverTimeout: NodeJS.Timeout | null = null;

    element.addEventListener('mouseenter', () => {
      if (hoverTimeout) clearTimeout(hoverTimeout);
      
      hoverTimeout = setTimeout(() => {
        popup.addTo(this.map);
      }, 100);
    });

    element.addEventListener('mouseleave', () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout);
        hoverTimeout = null;
      }
      popup.remove();
    });
  }

  /**
   * Clear all markers
   */
  clearAll(): void {
    this.renderedMarkers.forEach(rendered => {
      if (rendered.popup) {
        rendered.popup.remove();
      }
      markerPool.returnMarker(rendered.pooledMarker);
    });
    
    this.renderedMarkers.clear();
  }

  /**
   * Get render statistics
   */
  getStats(): { rendered: number; poolStats: any } {
    return {
      rendered: this.renderedMarkers.size,
      poolStats: markerPool.getStats()
    };
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.renderTimeout) {
      clearTimeout(this.renderTimeout);
    }
    
    this.clearAll();
    markerPool.cleanup();
  }
}

export default VirtualMarkerRenderer;
