/**
 * حماية متقدمة للكود ضد السرقة والتلاعب
 * تستخدم تقنيات متقدمة لحماية الكود المصدري
 */

const isProduction = import.meta.env.PROD;

/**
 * تشويش الكود وإخفاء المنطق
 */
export const obfuscateCode = (): void => {
  if (!isProduction) return;

  try {
    // إخفاء أسماء المتغيرات والدوال المهمة
    const sensitiveNames = [
      'supabase', 'mapbox', 'token', 'key', 'secret', 'password',
      'admin', 'auth', 'login', 'api', 'database', 'sql'
    ];

    // استبدال أسماء المتغيرات في الكونسول
    const originalLog = console.log;
    console.log = function(...args) {
      const filteredArgs = args.map(arg => {
        if (typeof arg === 'string') {
          let filtered = arg;
          sensitiveNames.forEach(name => {
            const regex = new RegExp(name, 'gi');
            filtered = filtered.replace(regex, '***');
          });
          return filtered;
        }
        return arg;
      });
      originalLog.apply(console, filteredArgs);
    };

    console.log('Code obfuscation enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * حماية ذكية للكود - تسمح بأدوات المطور والنقر الأيمن مع حماية المحتوى
 */
export const smartCodeProtection = (): void => {
  if (!isProduction) return;

  try {
    // كشف Safari
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

    // حماية ذكية لتحديد النص - السماح بالتحديد العادي مع حماية الكود الحساس
    document.addEventListener('selectstart', (e) => {
      if (e.target instanceof HTMLElement) {
        // السماح بتحديد النص في الحقول والمحتوى العادي
        const allowedElements = 'input, textarea, [contenteditable], .selectable, p, span, div:not(.code-protected), h1, h2, h3, h4, h5, h6';
        if (e.target.closest(allowedElements)) {
          return; // السماح بالتحديد
        }

        // منع تحديد الكود الحساس فقط
        if (e.target.closest('.code-protected, script, style')) {
          e.preventDefault();
        }
      }
    });

    // السماح بالسحب والإفلات للعناصر المسموحة
    document.addEventListener('dragstart', (e) => {
      if (e.target instanceof HTMLElement) {
        // السماح بسحب الملفات والعناصر المسموحة
        if (e.target.closest('input[type="file"], .draggable, img:not(.protected)')) {
          return; // السماح بالسحب
        }

        // منع سحب الكود الحساس فقط
        if (e.target.closest('.code-protected, script, style')) {
          e.preventDefault();
        }
      }
    });

    // حماية ذكية من الطباعة - إخفاء المحتوى الحساس فقط
    window.addEventListener('beforeprint', (e) => {
      // إخفاء العناصر الحساسة فقط عند الطباعة
      const sensitiveElements = document.querySelectorAll('.code-protected, script, style');
      sensitiveElements.forEach(el => {
        (el as HTMLElement).style.display = 'none';
      });

      // إظهار رسالة في المطبوع
      const printMessage = document.createElement('div');
      printMessage.className = 'print-only';
      printMessage.innerHTML = '<p>بعض المحتوى محمي ولا يظهر في الطباعة لأسباب أمنية</p>';
      printMessage.style.cssText = 'display: none; @media print { display: block; }';
      document.body.appendChild(printMessage);
    });

    // استعادة العناصر بعد الطباعة
    window.addEventListener('afterprint', (e) => {
      const sensitiveElements = document.querySelectorAll('.code-protected, script, style');
      sensitiveElements.forEach(el => {
        (el as HTMLElement).style.display = '';
      });

      const printMessage = document.querySelector('.print-only');
      if (printMessage) {
        printMessage.remove();
      }
    });

    // حماية من حفظ الصفحة مع السماح بحفظ النماذج
    document.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        // السماح بحفظ النماذج
        if (e.target instanceof HTMLElement &&
            e.target.closest('input, textarea, form')) {
          return; // السماح بحفظ النماذج
        }

        e.preventDefault();
        // رسالة أكثر ودية
        const message = 'لحماية المحتوى، يُرجى استخدام وظائف الحفظ المدمجة في التطبيق';
        if (isSafari) {
          alert(message);
        } else {
          console.warn(message);
        }
      }
    });

    console.log('Smart code protection enabled - DevTools and right-click allowed');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * مراقبة ذكية لأدوات التطوير - بدون منع الاستخدام
 */
export const monitorDevToolsUsage = (): void => {
  if (!isProduction) return;

  try {
    // كشف Safari
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

    // مراقبة استخدام أدوات التطوير بدون منع
    let devToolsUsageCount = 0;

    // تسجيل استخدام أدوات التطوير للإحصائيات فقط
    const logDevToolsUsage = () => {
      devToolsUsageCount++;
      console.log(`DevTools usage detected: ${devToolsUsageCount} times`);

      // إرسال إحصائية للخادم (اختياري)
      // fetch('/api/analytics/devtools-usage', { method: 'POST' });
    };

    // كشف فتح أدوات التطوير (للإحصائيات فقط)
    let windowHeight = window.innerHeight;
    let windowWidth = window.innerWidth;

    const checkWindowResize = () => {
      const heightDiff = Math.abs(window.innerHeight - windowHeight);
      const widthDiff = Math.abs(window.innerWidth - windowWidth);

      // حد أعلى لتجنب الإنذارات الكاذبة
      const threshold = 300;

      if (heightDiff > threshold || widthDiff > threshold) {
        logDevToolsUsage();
      }

      windowHeight = window.innerHeight;
      windowWidth = window.innerWidth;
    };

    // مراقبة تغيير حجم النافذة
    window.addEventListener('resize', checkWindowResize);

    // مراقبة استخدام الكونسول (للإحصائيات فقط)
    let consoleUsageCount = 0;
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;

    console.log = function(...args) {
      consoleUsageCount++;
      if (consoleUsageCount % 10 === 0) {
        console.info('Console usage detected for development purposes');
      }
      originalLog.apply(console, args);
    };

    console.warn = function(...args) {
      originalWarn.apply(console, args);
    };

    console.error = function(...args) {
      originalError.apply(console, args);
    };

    // رسالة ترحيب للمطورين
    setTimeout(() => {
      console.log(`
🛡️ مرحباً بك في تطبيق محطات نور!

هذا التطبيق محمي بتقنيات أمان متقدمة.
يمكنك استخدام أدوات المطور بحرية للتعلم والاستكشاف.

ملاحظة: المحتوى الحساس محمي ولا يمكن الوصول إليه.

للمطورين: إذا كنت مهتماً بالعمل معنا، تواصل معنا!
      `);
    }, 2000);

    console.log('DevTools monitoring enabled (non-intrusive mode)');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * حماية من تحليل الشبكة
 */
export const protectNetworkAnalysis = (): void => {
  if (!isProduction) return;

  try {
    // تشفير URLs الحساسة
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
      // إضافة headers عشوائية لتشويش التحليل
      const randomHeaders = {
        'X-Request-ID': Math.random().toString(36),
        'X-Timestamp': Date.now().toString(),
        'X-Client-Version': '1.0.0'
      };

      const newInit = {
        ...init,
        headers: {
          ...init?.headers,
          ...randomHeaders
        }
      };

      return originalFetch(input, newInit);
    };

    console.log('Network analysis protection enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * تهيئة جميع آليات الحماية المتقدمة - الإصدار الودود
 */
export const initializeAdvancedProtection = (): void => {
  if (!isProduction) {
    console.log('Advanced protection disabled in development mode');
    return;
  }

  try {
    obfuscateCode();
    smartCodeProtection(); // بدلاً من preventCodeCopy
    monitorDevToolsUsage(); // بدلاً من detectDevToolsAdvanced
    protectNetworkAnalysis();

    console.log('🛡️ Smart protection initialized - DevTools and right-click enabled');
  } catch (error) {
    console.error('Failed to initialize advanced protection:', error);
  }
};

export default {
  initializeAdvancedProtection,
  obfuscateCode,
  smartCodeProtection,
  monitorDevToolsUsage,
  protectNetworkAnalysis
};
