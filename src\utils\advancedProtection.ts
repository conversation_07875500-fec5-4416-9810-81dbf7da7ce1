/**
 * حماية متقدمة للكود ضد السرقة والتلاعب
 * تستخدم تقنيات متقدمة لحماية الكود المصدري
 */

const isProduction = import.meta.env.PROD;

/**
 * تشويش الكود وإخفاء المنطق
 */
export const obfuscateCode = (): void => {
  if (!isProduction) return;

  try {
    // إخفاء أسماء المتغيرات والدوال المهمة
    const sensitiveNames = [
      'supabase', 'mapbox', 'token', 'key', 'secret', 'password',
      'admin', 'auth', 'login', 'api', 'database', 'sql'
    ];

    // استبدال أسماء المتغيرات في الكونسول
    const originalLog = console.log;
    console.log = function(...args) {
      const filteredArgs = args.map(arg => {
        if (typeof arg === 'string') {
          let filtered = arg;
          sensitiveNames.forEach(name => {
            const regex = new RegExp(name, 'gi');
            filtered = filtered.replace(regex, '***');
          });
          return filtered;
        }
        return arg;
      });
      originalLog.apply(console, filteredArgs);
    };

    console.log('Code obfuscation enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * حماية من نسخ الكود
 */
export const preventCodeCopy = (): void => {
  if (!isProduction) return;

  try {
    // منع تحديد النص
    document.addEventListener('selectstart', (e) => {
      if (e.target instanceof HTMLElement && 
          !e.target.closest('input, textarea, [contenteditable]')) {
        e.preventDefault();
      }
    });

    // منع السحب والإفلات
    document.addEventListener('dragstart', (e) => {
      e.preventDefault();
    });

    // منع الطباعة
    window.addEventListener('beforeprint', (e) => {
      e.preventDefault();
      alert('الطباعة غير مسموحة لأسباب أمنية');
    });

    // منع حفظ الصفحة
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        alert('حفظ الصفحة غير مسموح لأسباب أمنية');
      }
    });

    console.log('Code copy protection enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * كشف أدوات التطوير المتقدم
 */
export const detectDevToolsAdvanced = (): void => {
  if (!isProduction) return;

  try {
    let devToolsOpen = false;
    
    // طريقة 1: قياس وقت debugger
    const detectDebugger = () => {
      const start = performance.now();
      debugger;
      const end = performance.now();
      return end - start > 100;
    };

    // طريقة 2: كشف تغيير حجم النافذة
    let windowHeight = window.innerHeight;
    let windowWidth = window.innerWidth;

    const checkWindowSize = () => {
      const heightDiff = Math.abs(window.innerHeight - windowHeight);
      const widthDiff = Math.abs(window.innerWidth - windowWidth);
      
      if (heightDiff > 200 || widthDiff > 200) {
        return true;
      }
      
      windowHeight = window.innerHeight;
      windowWidth = window.innerWidth;
      return false;
    };

    // طريقة 3: كشف console.clear
    let clearCount = 0;
    const originalClear = console.clear;
    console.clear = function() {
      clearCount++;
      if (clearCount > 5) {
        devToolsOpen = true;
      }
      originalClear.apply(console);
    };

    // فحص دوري
    setInterval(() => {
      if (detectDebugger() || checkWindowSize() || devToolsOpen) {
        // إخفاء المحتوى الحساس
        document.body.style.filter = 'blur(10px)';
        
        // عرض رسالة تحذير
        const warning = document.createElement('div');
        warning.innerHTML = `
          <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                      background: rgba(0,0,0,0.9); z-index: 999999; 
                      display: flex; align-items: center; justify-content: center;
                      color: white; font-size: 24px; text-align: center;">
            <div>
              <h2>🔒 تحذير أمني</h2>
              <p>تم اكتشاف محاولة الوصول لأدوات التطوير</p>
              <p>هذا التطبيق محمي ضد السرقة والتلاعب</p>
              <button onclick="location.reload()" 
                      style="padding: 10px 20px; font-size: 16px; margin-top: 20px;">
                إعادة تحميل
              </button>
            </div>
          </div>
        `;
        document.body.appendChild(warning);
      }
    }, 2000);

    console.log('Advanced DevTools detection enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * حماية من تحليل الشبكة
 */
export const protectNetworkAnalysis = (): void => {
  if (!isProduction) return;

  try {
    // تشفير URLs الحساسة
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
      // إضافة headers عشوائية لتشويش التحليل
      const randomHeaders = {
        'X-Request-ID': Math.random().toString(36),
        'X-Timestamp': Date.now().toString(),
        'X-Client-Version': '1.0.0'
      };

      const newInit = {
        ...init,
        headers: {
          ...init?.headers,
          ...randomHeaders
        }
      };

      return originalFetch(input, newInit);
    };

    console.log('Network analysis protection enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * تهيئة جميع آليات الحماية المتقدمة
 */
export const initializeAdvancedProtection = (): void => {
  if (!isProduction) {
    console.log('Advanced protection disabled in development mode');
    return;
  }

  try {
    obfuscateCode();
    preventCodeCopy();
    detectDevToolsAdvanced();
    protectNetworkAnalysis();
    
    console.log('🛡️ Advanced protection initialized successfully');
  } catch (error) {
    console.error('Failed to initialize advanced protection:', error);
  }
};

export default {
  initializeAdvancedProtection,
  obfuscateCode,
  preventCodeCopy,
  detectDevToolsAdvanced,
  protectNetworkAnalysis
};
