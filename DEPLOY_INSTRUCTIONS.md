# تعليمات النشر السريع

## ✅ تم إصلاح المشكلة!

تم حل مشكلة الصفحة البيضاء على Cloudflare Pages من خلال:

### 1. إصلاح مشكلة React Context
- تم تحديث `src/main.tsx` لاستخدام `import.meta.env.DEV` بدلاً من `process.env.NODE_ENV`
- تم تحسين `public/process-shim.js` للتوافق مع Cloudflare

### 2. إضافة ملفات Cloudflare
- `dist/_headers` - إعدادات الأمان والتخزين المؤقت
- `dist/_redirects` - دعم React Router
- `wrangler.toml` - إعدادات المشروع

## 🚀 خطوات النشر

### الطريقة الأولى: رفع مجلد dist مباشرة
1. اضغط على مجلد `dist` واضغط "Upload to Cloudflare Pages"
2. أو ارفع محتويات مجلد `dist` إلى Cloudflare Pages

### الطريقة الثانية: ربط GitHub
1. ارفع المشروع إلى GitHub:
   ```bash
   git add .
   git commit -m "Fix Cloudflare deployment"
   git push origin main
   ```

2. في Cloudflare Pages:
   - Build command: `npm run build`
   - Build output directory: `dist`
   - Root directory: `/`

### متغيرات البيئة المطلوبة
```
NODE_ENV=production
VITE_SUPABASE_URL=https://jtnqcyouncjoebqcalzh.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
VITE_MAPBOX_TOKEN=pk.eyJ1Ijoia2FyaW1mYXllZCIsImEiOiJjbTllOXpmMHQxNWZ5MmlzN3ZzZW55cnAyIn0...
```

## ✅ التحقق من النجاح
بعد النشر، تحقق من:
1. الموقع يفتح بدون صفحة بيضاء
2. لا توجد أخطاء في الكونسول
3. الخريطة تعمل بشكل صحيح
4. تسجيل الدخول يعمل

## 📞 الدعم
إذا واجهت مشاكل، تحقق من:
- سجلات البناء في Cloudflare Dashboard
- الكونسول في المتصفح للأخطاء
- متغيرات البيئة مضبوطة بشكل صحيح
