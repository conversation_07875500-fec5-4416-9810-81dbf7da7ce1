
import { But<PERSON> } from "@/components/ui/button";
import { NavigationMenu, NavigationMenuItem, NavigationMenuList } from "@/components/ui/navigation-menu";
import { useLanguage } from "@/i18n/LanguageContext";
import LanguageSwitcher from "./LanguageSwitcher";
import { Link } from "react-router-dom";
import HomeSidebar from "./HomeSidebar";

const Header = () => {
  const { language, t, dir } = useLanguage();

  return (
    <header className={`w-full bg-gradient-to-r from-noor-purple to-noor-orange py-2 px-2 sm:px-4 md:px-6`} dir={dir}>
      <div className="container mx-auto">
        {/* Header Row: Menu, Title, Admin <PERSON>ton */}
        <div className={`flex items-center justify-between min-h-[56px] gap-2`}> 
          {/* زر القائمة - الجانب المعاكس حسب اللغة */}
              {language === 'ar' ? (
                <>
                  {/* زر القائمة في أقصى اليسار */}
                  <div className="flex-shrink-0 order-first ml-2 sm:ml-4">
                    <HomeSidebar />
                  </div>
                  {/* العنوان والشعار */}
                  <div className="flex-1 flex items-center justify-center">
                    <h1 className="text-white text-lg sm:text-xl md:text-2xl font-bold flex items-center gap-2 sm:gap-4">
                      <span className="hidden xs:inline">مرحباً بك في محطات نور</span>
                      <span className="xs:hidden">محطات نور</span>
                      <div className="relative mr-2">
                        <img
                          src="https://noor.com.sa/wp-content/themes/noor/images/apple-touch-icon-72x72.png"
                          alt="Noor Logo"
                          className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 animate-spin-slow"
                          style={{ animationDuration: '15s' }}
                        />
                        <div className="absolute inset-0 bg-white rounded-full blur-md opacity-30 animate-pulse" style={{ animationDuration: '3s' }}></div>
                      </div>
                    </h1>
                  </div>
                </>
              ) : (
                <>
                  {/* زر القائمة في أقصى اليمين */}
                  <div className="flex-shrink-0 mr-2 sm:mr-4">
                    <HomeSidebar />
                  </div>
                  {/* العنوان والشعار */}
                  <div className="flex-1 flex items-center justify-center">
                    <h1 className="text-white text-lg sm:text-xl md:text-2xl font-bold flex items-center gap-2 sm:gap-4">
                      <span className="hidden xs:inline">Welcome to Noor Stations</span>
                      <span className="xs:hidden">Noor Stations</span>
                      <div className="relative mr-2">
                        <img
                          src="https://noor.com.sa/wp-content/themes/noor/images/apple-touch-icon-72x72.png"
                          alt="Noor Logo"
                          className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 animate-spin-slow"
                          style={{ animationDuration: '15s' }}
                        />
                        <div className="absolute inset-0 bg-white rounded-full blur-md opacity-30 animate-pulse" style={{ animationDuration: '3s' }}></div>
                      </div>
                    </h1>
                  </div>
                </>
              )}

        {/* زر لوحة التحكم (تمت إزالته هنا بناءً على طلب المستخدم) */}
        </div>

              {/* زر لوحة التحكم تمت إزالته من الهيدر */}
        <div className="hidden md:flex items-center justify-center mt-2">
          <NavigationMenu className="flex-row">
            <NavigationMenuList className="flex flex-row gap-2">
              <NavigationMenuItem>
                <Link to="/">
                  <Button variant="ghost" className="text-white text-sm h-8 px-2 md:h-10 md:px-4">
                    {t('common', 'home')}
                  </Button>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link to="/services">
                  <Button variant="ghost" className="text-white text-sm h-8 px-2 md:h-10 md:px-4">
                    {language === 'ar' ? 'الخدمات' : 'Services'}
                  </Button>
                </Link>
              </NavigationMenuItem>
              {/* <NavigationMenuItem>
                <Link to="/about">
                  <Button variant="ghost" className="text-white text-sm h-8 px-2 md:h-10 md:px-4">
                    {language === 'ar' ? 'عن نور' : 'About Noor'}
                  </Button>
                </Link>
              </NavigationMenuItem> */}
              <NavigationMenuItem>
                <Link to="/contact">
                  <Button variant="ghost" className="text-white text-sm h-8 px-2 md:h-10 md:px-4">
                    {language === 'ar' ? 'اتصل بنا' : 'Contact Us'}
                  </Button>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <LanguageSwitcher
                  variant="secondary"
                  className="bg-white text-noor-purple hover:bg-white/90 text-sm h-8 px-2 md:h-10 md:px-4"
                />
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>
        </div>
      </div>
    </header>
  );
};

export default Header;
