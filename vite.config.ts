import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    host: "::",
    port: 5173,
    proxy: {
      // Proxy API requests to Supabase Edge Functions
      '/api/create-admin-account': {
        target: 'https://jtnqcyouncjoebqcalzh.supabase.co/functions/v1/create-admin-account',
        changeOrigin: true,
        rewrite: (_path) => '',
      },
      hmr: {
        // overlay: false, // تم الحذف لأن الخاصية غير مدعومة هنا
      },
    }
  },
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  optimizeDeps: {
    include: ['mapbox-gl', '@mapbox/mapbox-gl-geocoder'],
    exclude: [],
  },
  // تعريف متغيرات البيئة - مبسط للتوافق مع Cloudflare
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production'),
    'global': 'globalThis',
  },
  build: {
    // استهدف ES2020 لضمان التوافق مع Cloudflare
    target: 'es2020',
    // استخدم Terser مع إعدادات حماية إضافية
    minify: 'terser',
    // رفع حد حجم الملف لتجنب التحذيرات غير الضرورية
    chunkSizeWarningLimit: 1500,
    // إعدادات إضافية للتوافق مع Cloudflare
    outDir: 'dist',
    assetsDir: 'assets',
    emptyOutDir: true,
    // ضمان أن ملفات CSS لها الامتداد الصحيح
    cssCodeSplit: true,
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
      mangle: true,
      format: {
        comments: false,
      },
    },
    rollupOptions: {
      output: {
        // تعطيل تقسيم الكود لتجنب مشاكل التحميل في Cloudflare
        manualChunks: undefined,
        // دمج جميع الملفات في ملف واحد
        inlineDynamicImports: true,
      },
    },
    // تحسين تقسيم الكود
    sourcemap: false, // تعطيل source maps في الإنتاج
    // تحسين الأداء
    reportCompressedSize: false, // تعطيل تقارير حجم الضغط لتسريع البناء
  },
});
