import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    host: "::",
    port: 8980,
    proxy: {
      // Proxy API requests to Supabase Edge Functions
      '/api/create-admin-account': {
        target: 'https://jtnqcyouncjoebqcalzh.supabase.co/functions/v1/create-admin-account',
        changeOrigin: true,
        rewrite: (_path) => '',
      },
      hmr: {
        // overlay: false, // تم الحذف لأن الخاصية غير مدعومة هنا
      },
    }
  },
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  optimizeDeps: {
    include: ['mapbox-gl', '@mapbox/mapbox-gl-geocoder'],
    exclude: [],
  },
  // تعريف متغيرات البيئة
  define: {
    // تعريف متغير process.env لحل مشكلة "process is not defined"
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production'),
    // إضافة متغيرات إضافية للتوافق
    'process.browser': true,
    'process.version': '""',
    'process.versions': '{}',
    // تعريف global للتوافق مع بعض المكتبات
    'global': 'globalThis',
  },
  build: {
    // استهدف ES2020 لضمان التوافق مع Cloudflare
    target: 'es2020',
    // استخدم Terser مع إعدادات حماية إضافية
    minify: 'terser',
    // رفع حد حجم الملف لتجنب التحذيرات غير الضرورية
    chunkSizeWarningLimit: 1500,
    // إعدادات إضافية للتوافق مع Cloudflare
    outDir: 'dist',
    assetsDir: 'assets',
    emptyOutDir: true,
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
      mangle: true,
      format: {
        comments: false,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Bundle core dependencies together
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
              return 'vendor-react';
            }
            if (id.includes('@radix-ui')) {
              return 'vendor-ui';
            }
            if (id.includes('mapbox-gl') || id.includes('@mapbox')) {
              return 'vendor-map';
            }
            if (id.includes('@supabase') || id.includes('axios') || id.includes('date-fns')) {
              return 'vendor-utils';
            }
            // Additional third-party dependencies
            return 'vendor';
          }
        },
      },
    },
    // تحسين تقسيم الكود
    cssCodeSplit: true, // فصل ملفات CSS
    sourcemap: false, // تعطيل source maps في الإنتاج
    // تحسين الأداء
    reportCompressedSize: false, // تعطيل تقارير حجم الضغط لتسريع البناء
  },
});
