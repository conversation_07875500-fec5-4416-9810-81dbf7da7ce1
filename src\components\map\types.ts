
export interface SaudiCity {
  name: string;
  nameEn: string;
  latitude: number;
  longitude: number;
  zoom: number;
}

export interface MapTexts {
  getLocation: string;
  directions: string;
  nearestStation: string;
  reset: string;
  findNearest: string;
  locationDetecting: string;
  pleaseWait: string;
  locationDetected: string;
  nearestStationIs: string;
  showingDirections: string;
  directionsTo: string;
  meters: string;
  kilometers: string;
  locationError: string;
  enableLocation: string;
  fuelTypes: string;
  region: string;
  subRegion: string;
  distance: string;
  name: string;
  clickForDetails: string;
  selectCity: string;
  searchStation: string;
  noResults: string;
  searchResults: string;
}
