/* Optimized marker styles - Fixed positioning for Mapbox */
.marker-pin {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  transition: transform 0.3s ease-in-out;
  transform-origin: center bottom;
  will-change: transform;
  width: 28px;
  height: 28px;
  z-index: 1;
  /* Critical: Let Mapbox handle positioning completely */
  position: absolute !important;
  display: block !important;
  /* Ensure no margin or padding interferes with Mapbox positioning */
  margin: 0 !important;
  padding: 0 !important;
  /* Prevent any layout issues */
  box-sizing: border-box !important;
  /* Ensure proper stacking */
  pointer-events: auto !important;
}

.marker-pin.selected {
  width: 40px;
  height: 40px;
  background-position: center center;
  z-index: 1000 !important;
  animation: bounce 1s infinite alternate;
  opacity: 1;
  pointer-events: auto;
  transform: scale(1.2) !important;
  /* Ensure selected markers are properly positioned */
  position: absolute !important;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* Hover effect */
.marker-hover {
  filter: drop-shadow(0 6px 10px rgba(0, 0, 0, 0.4));
}

/* Remove specific mapboxgl marker styling to prevent conflicts */

/* Bounce animation with the specific transform you requested */
@keyframes bounce {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-10px);
  }
}

/* Optimize animations for low-performance devices */
@media (prefers-reduced-motion: reduce) {
  .marker-pin.selected {
    animation: none;
  }
}

/* Station popup styling */
.station-popup {
  z-index: 9999 !important;
}

.station-popup .mapboxgl-popup-content {
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Optimize popup rendering */
.mapboxgl-popup {
  will-change: transform;
}
