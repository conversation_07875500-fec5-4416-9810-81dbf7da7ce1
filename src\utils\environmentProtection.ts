/**
 * حماية متغيرات البيئة والمفاتيح الحساسة
 */

const isProduction = import.meta.env.PROD;

/**
 * تشفير المفاتيح الحساسة
 */
export const encryptSensitiveKeys = (): void => {
  if (!isProduction) return;

  try {
    // قائمة المفاتيح الحساسة
    const sensitiveKeys = [
      'VITE_SUPABASE_ANON_KEY',
      'VITE_MAPBOX_TOKEN',
      'VITE_SUPABASE_URL'
    ];

    // إخفاء المفاتيح من window object
    sensitiveKeys.forEach(key => {
      if (window[key as any]) {
        delete window[key as any];
      }
    });

    // منع الوصول لمتغيرات البيئة عبر الكونسول
    Object.defineProperty(window, 'import', {
      get: () => {
        console.warn('Access to import.meta.env is restricted');
        return undefined;
      },
      configurable: false
    });

    console.log('Environment protection enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * حماية localStorage و sessionStorage
 */
export const protectStorage = (): void => {
  if (!isProduction) return;

  try {
    // تشفير البيانات المخزنة
    const originalSetItem = localStorage.setItem;
    const originalGetItem = localStorage.getItem;

    localStorage.setItem = function(key: string, value: string) {
      // تشفير بسيط للبيانات الحساسة
      if (key.includes('token') || key.includes('key') || key.includes('auth')) {
        const encrypted = btoa(value + '_protected_' + Date.now());
        originalSetItem.call(this, key, encrypted);
      } else {
        originalSetItem.call(this, key, value);
      }
    };

    localStorage.getItem = function(key: string) {
      const value = originalGetItem.call(this, key);
      if (value && (key.includes('token') || key.includes('key') || key.includes('auth'))) {
        try {
          const decoded = atob(value);
          const parts = decoded.split('_protected_');
          return parts[0];
        } catch {
          return value;
        }
      }
      return value;
    };

    console.log('Storage protection enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * منع تحليل الكود المصدري
 */
export const preventSourceAnalysis = (): void => {
  if (!isProduction) return;

  try {
    // منع عرض المصدر
    document.addEventListener('keydown', (e) => {
      // منع Ctrl+U (عرض المصدر)
      if (e.ctrlKey && e.key === 'u') {
        e.preventDefault();
        alert('عرض المصدر غير مسموح لأسباب أمنية');
      }
      
      // منع Ctrl+Shift+C (فحص العنصر)
      if (e.ctrlKey && e.shiftKey && e.key === 'C') {
        e.preventDefault();
        alert('فحص العناصر غير مسموح لأسباب أمنية');
      }
    });

    // منع النقر بالزر الأيمن
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      alert('النقر بالزر الأيمن غير مسموح لأسباب أمنية');
    });

    // إخفاء معلومات التطبيق
    Object.defineProperty(navigator, 'userAgent', {
      get: () => 'Protected Application',
      configurable: false
    });

    console.log('Source analysis prevention enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * حماية من استخراج البيانات
 */
export const preventDataExtraction = (): void => {
  if (!isProduction) return;

  try {
    // منع استخدام JSON.stringify على البيانات الحساسة
    const originalStringify = JSON.stringify;
    JSON.stringify = function(value: any, replacer?: any, space?: any) {
      if (typeof value === 'object' && value !== null) {
        // إزالة البيانات الحساسة
        const cleaned = { ...value };
        const sensitiveFields = ['password', 'token', 'key', 'secret', 'auth'];
        
        sensitiveFields.forEach(field => {
          if (cleaned[field]) {
            cleaned[field] = '***PROTECTED***';
          }
        });
        
        return originalStringify.call(this, cleaned, replacer, space);
      }
      return originalStringify.call(this, value, replacer, space);
    };

    // حماية من استخراج HTML
    const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
    if (originalInnerHTML) {
      Object.defineProperty(Element.prototype, 'innerHTML', {
        get: function() {
          const content = originalInnerHTML.get?.call(this);
          if (content && content.includes('sensitive')) {
            return '<!-- Content Protected -->';
          }
          return content;
        },
        set: originalInnerHTML.set,
        configurable: false
      });
    }

    console.log('Data extraction prevention enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * تهيئة جميع آليات حماية البيئة
 */
export const initializeEnvironmentProtection = (): void => {
  if (!isProduction) {
    console.log('Environment protection disabled in development mode');
    return;
  }

  try {
    encryptSensitiveKeys();
    protectStorage();
    preventSourceAnalysis();
    preventDataExtraction();
    
    console.log('🔐 Environment protection initialized successfully');
  } catch (error) {
    console.error('Failed to initialize environment protection:', error);
  }
};

export default {
  initializeEnvironmentProtection,
  encryptSensitiveKeys,
  protectStorage,
  preventSourceAnalysis,
  preventDataExtraction
};
