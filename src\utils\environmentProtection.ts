/**
 * حماية متغيرات البيئة والمفاتيح الحساسة
 */

const isProduction = import.meta.env.PROD;

/**
 * تشفير المفاتيح الحساسة
 */
export const encryptSensitiveKeys = (): void => {
  if (!isProduction) return;

  try {
    // قائمة المفاتيح الحساسة
    const sensitiveKeys = [
      'VITE_SUPABASE_ANON_KEY',
      'VITE_MAPBOX_TOKEN',
      'VITE_SUPABASE_URL'
    ];

    // إخفاء المفاتيح من window object
    sensitiveKeys.forEach(key => {
      if (window[key as any]) {
        delete window[key as any];
      }
    });

    // منع الوصول لمتغيرات البيئة عبر الكونسول
    Object.defineProperty(window, 'import', {
      get: () => {
        console.warn('Access to import.meta.env is restricted');
        return undefined;
      },
      configurable: false
    });

    console.log('Environment protection enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * حماية localStorage و sessionStorage
 */
export const protectStorage = (): void => {
  if (!isProduction) return;

  try {
    // تشفير البيانات المخزنة
    const originalSetItem = localStorage.setItem;
    const originalGetItem = localStorage.getItem;

    localStorage.setItem = function(key: string, value: string) {
      // تشفير بسيط للبيانات الحساسة
      if (key.includes('token') || key.includes('key') || key.includes('auth')) {
        const encrypted = btoa(value + '_protected_' + Date.now());
        originalSetItem.call(this, key, encrypted);
      } else {
        originalSetItem.call(this, key, value);
      }
    };

    localStorage.getItem = function(key: string) {
      const value = originalGetItem.call(this, key);
      if (value && (key.includes('token') || key.includes('key') || key.includes('auth'))) {
        try {
          const decoded = atob(value);
          const parts = decoded.split('_protected_');
          return parts[0];
        } catch {
          return value;
        }
      }
      return value;
    };

    console.log('Storage protection enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * حماية ذكية للمصدر - تسمح بأدوات المطور والنقر الأيمن
 */
export const smartSourceProtection = (): void => {
  if (!isProduction) return;

  try {
    // كشف Safari
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

    // السماح بجميع اختصارات أدوات المطور
    // لا نمنع Ctrl+U, Ctrl+Shift+C, F12, إلخ

    // السماح بالنقر بالزر الأيمن بالكامل
    // لا نمنع contextmenu

    // حماية ذكية للمحتوى الحساس فقط
    document.addEventListener('copy', (e) => {
      const selection = window.getSelection()?.toString();
      if (selection) {
        // فحص إذا كان المحتوى المنسوخ يحتوي على كود حساس
        const sensitivePatterns = [
          /VITE_SUPABASE_ANON_KEY/,
          /VITE_MAPBOX_TOKEN/,
          /supabase\.co/,
          /pk\.eyJ/,
          /Bearer\s+/,
          /token.*:/,
          /key.*:/,
          /secret.*:/
        ];

        const containsSensitiveData = sensitivePatterns.some(pattern =>
          pattern.test(selection)
        );

        if (containsSensitiveData) {
          e.preventDefault();
          // نسخ نسخة منظفة
          const cleanedContent = selection.replace(/VITE_SUPABASE_ANON_KEY.*/, '[PROTECTED_KEY]')
                                          .replace(/VITE_MAPBOX_TOKEN.*/, '[PROTECTED_TOKEN]')
                                          .replace(/pk\.eyJ[^"'\s]*/, '[PROTECTED_MAPBOX_TOKEN]');

          navigator.clipboard.writeText(cleanedContent).then(() => {
            console.log('Sensitive data cleaned from clipboard');
          });
        }
      }
    });

    // حماية من استخراج البيانات الحساسة عبر JavaScript
    const originalStringify = JSON.stringify;
    JSON.stringify = function(value: any, replacer?: any, space?: any) {
      if (typeof value === 'object' && value !== null) {
        const cleaned = { ...value };
        const sensitiveFields = ['anon_key', 'mapbox_token', 'supabase_url', 'password', 'token', 'key', 'secret'];

        sensitiveFields.forEach(field => {
          if (cleaned[field]) {
            cleaned[field] = '[PROTECTED]';
          }
        });

        return originalStringify.call(this, cleaned, replacer, space);
      }
      return originalStringify.call(this, value, replacer, space);
    };

    // رسالة ترحيب للمطورين
    console.log(`
🔓 أدوات المطور والنقر الأيمن مفعلان!

يمكنك استخدام:
✅ F12 - أدوات المطور
✅ Ctrl+Shift+I - فحص العناصر
✅ Ctrl+U - عرض المصدر
✅ النقر الأيمن - القائمة السياقية
✅ تحديد ونسخ النص

ملاحظة: البيانات الحساسة محمية تلقائياً عند النسخ.
    `);

    console.log('Smart source protection enabled - Full access granted');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * حماية من استخراج البيانات
 */
export const preventDataExtraction = (): void => {
  if (!isProduction) return;

  try {
    // منع استخدام JSON.stringify على البيانات الحساسة
    const originalStringify = JSON.stringify;
    JSON.stringify = function(value: any, replacer?: any, space?: any) {
      if (typeof value === 'object' && value !== null) {
        // إزالة البيانات الحساسة
        const cleaned = { ...value };
        const sensitiveFields = ['password', 'token', 'key', 'secret', 'auth'];
        
        sensitiveFields.forEach(field => {
          if (cleaned[field]) {
            cleaned[field] = '***PROTECTED***';
          }
        });
        
        return originalStringify.call(this, cleaned, replacer, space);
      }
      return originalStringify.call(this, value, replacer, space);
    };

    // حماية من استخراج HTML
    const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
    if (originalInnerHTML) {
      Object.defineProperty(Element.prototype, 'innerHTML', {
        get: function() {
          const content = originalInnerHTML.get?.call(this);
          if (content && content.includes('sensitive')) {
            return '<!-- Content Protected -->';
          }
          return content;
        },
        set: originalInnerHTML.set,
        configurable: false
      });
    }

    console.log('Data extraction prevention enabled');
  } catch (error) {
    // تجاهل الأخطاء
  }
};

/**
 * تهيئة جميع آليات حماية البيئة - الإصدار الودود
 */
export const initializeEnvironmentProtection = (): void => {
  if (!isProduction) {
    console.log('Environment protection disabled in development mode');
    return;
  }

  try {
    encryptSensitiveKeys();
    protectStorage();
    smartSourceProtection(); // بدلاً من preventSourceAnalysis
    preventDataExtraction();

    console.log('🔐 Smart environment protection initialized - Developer friendly');
  } catch (error) {
    console.error('Failed to initialize environment protection:', error);
  }
};

export default {
  initializeEnvironmentProtection,
  encryptSensitiveKeys,
  protectStorage,
  smartSourceProtection,
  preventDataExtraction
};
