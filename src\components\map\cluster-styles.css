/* Cluster styling */
.cluster-marker {
  background-color: #7c3aed;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  will-change: transform;
}

.cluster-marker:hover {
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.4);
}

/* Pulse animation for clusters */
@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.9; }
  100% { transform: scale(1); opacity: 1; }
}

.cluster-marker {
  animation: pulse 1.5s infinite ease-in-out;
}

/* Optimize animations for low-performance devices */
@media (prefers-reduced-motion: reduce) {
  .cluster-marker {
    animation: none;
  }
}
