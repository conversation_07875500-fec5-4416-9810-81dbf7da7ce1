// إصلاح شامل لمشاكل Cloudflare Pages
(function() {
  'use strict';
  
  console.log('Cloudflare fix script starting...');
  
  // 1. إصلاح مشكلة MIME types
  const originalFetch = window.fetch;
  window.fetch = function(input, init) {
    const url = typeof input === 'string' ? input : input.url;
    
    if (url.endsWith('.css')) {
      return originalFetch(input, {
        ...init,
        headers: {
          ...init?.headers,
          'Accept': 'text/css,*/*;q=0.1'
        }
      }).then(response => {
        if (response.headers.get('content-type')?.includes('javascript')) {
          console.warn('CSS file served with wrong MIME type, fixing...', url);
          return new Response(response.body, {
            status: response.status,
            statusText: response.statusText,
            headers: {
              ...response.headers,
              'content-type': 'text/css'
            }
          });
        }
        return response;
      });
    }
    
    return originalFetch(input, init);
  };
  
  // 2. إصلاح مشكلة React Context
  let reactLoadAttempts = 0;
  const maxAttempts = 50;
  
  function waitForReact() {
    reactLoadAttempts++;
    
    if (window.React && !window.React.__isMock && window.React.createContext) {
      console.log('React loaded successfully!');
      return;
    }
    
    if (reactLoadAttempts < maxAttempts) {
      setTimeout(waitForReact, 100);
    } else {
      console.error('React failed to load after', maxAttempts, 'attempts');
      // محاولة إعادة تحميل الصفحة مرة واحدة فقط
      if (!sessionStorage.getItem('react-reload-attempted')) {
        sessionStorage.setItem('react-reload-attempted', 'true');
        console.log('Attempting to reload page...');
        window.location.reload();
      }
    }
  }
  
  // 3. بدء مراقبة تحميل React
  setTimeout(waitForReact, 1000);
  
  // 4. إضافة معالج للأخطاء
  window.addEventListener('error', function(event) {
    if (event.message.includes('createContext')) {
      console.error('React createContext error detected:', event.message);
      
      // محاولة إصلاح فوري
      if (!window.React || window.React.__isMock) {
        console.log('Attempting to fix React...');
        
        // إعادة تحميل السكريبتات
        const scripts = document.querySelectorAll('script[src*="vendor-react"]');
        scripts.forEach(script => {
          const newScript = document.createElement('script');
          newScript.src = script.src + '?reload=' + Date.now();
          newScript.type = 'module';
          document.head.appendChild(newScript);
        });
      }
    }
  });
  
  // 5. إضافة معالج لتحميل الصفحة
  window.addEventListener('load', function() {
    setTimeout(function() {
      if (!window.React || window.React.__isMock) {
        console.warn('React still not loaded after page load');
      } else {
        console.log('React confirmed loaded after page load');
        // إزالة علامة إعادة التحميل
        sessionStorage.removeItem('react-reload-attempted');
      }
    }, 2000);
  });
  
  console.log('Cloudflare fix script loaded successfully');
})();
