/**
 * وحدة لتأمين التطبيق بشكل عام
 * توفر وظائف أمان عامة للتطبيق
 * تم تحديثها لتوفير حماية متقدمة للكود والبيانات
 */

import secureStorage from "./secureStorage";
import secureApi from "./secureApi";
import secureExcel from "./secureExcel";
import secureIO from "./secureIO";
import secureFiles from "./secureFiles";
import secureCommunication from "./secureCommunication";
import { supabase } from "@/integrations/supabase/client";
import codeProtection from "./codeProtection";
import tokenProtection from "./tokenProtection";
import tamperProtection from "./tamperProtection";
import sessionManager from "./sessionManager";
import logger from "@/utils/logger";
import { User } from '@supabase/supabase-js';

/**
 * التحقق من صلاحيات المستخدم
 * @param requiredRole الدور المطلوب
 * @returns وعد بنتيجة التحقق
 */
export const checkUserPermission = async (requiredRole: 'admin' | 'owner'): Promise<boolean> => {
  try {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) return false;

    const { data: userData, error } = await supabase
      .from('admin_users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (error || !userData) return false;

    if (requiredRole === 'owner') {
      return userData.role === 'owner';
    }

    return userData.role === 'admin' || userData.role === 'owner';
  } catch (error) {
    console.error('فشل في التحقق من صلاحيات المستخدم:', error);
    return false;
  }
};

/**
 * التحقق من حالة تسجيل الدخول
 * @returns وعد بحالة تسجيل الدخول
 */
export const isLoggedIn = async (): Promise<boolean> => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    return !!session;
  } catch (error) {
    console.error('فشل في التحقق من حالة تسجيل الدخول:', error);
    return false;
  }
};

/**
 * الحصول على معلومات المستخدم الحالي
 * @returns وعد بمعلومات المستخدم
 */
interface AdminUser {
  id: string;
  role: 'admin' | 'owner';
  name?: string;
  email: string;
  created_at: string;
}

export const getCurrentUser = async (): Promise<AdminUser | null> => {
  try {
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) return null;

    const { data: userData, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('id', session.user.id)
      .single();

    if (error || !userData) return null;
    return userData;
  } catch (error) {
    logger.error('Error getting current user:', error);
    return null;
  }
};

/**
 * تسجيل خروج آمن
 */
export const secureLogout = async (): Promise<void> => {
  try {
    // تسجيل الخروج من supabase
    await supabase.auth.signOut();
    
    // مسح بيانات الجلسة
    sessionManager.clearSessionData();
    
    // إضافة إجراءات تنظيف إضافية
    secureStorage.clear();
    logger.info('تم تسجيل الخروج بنجاح');
  } catch (error) {
    logger.error('فشل في تسجيل الخروج:', error);
    throw error;
  }
};

/**
 * تأمين التطبيق عند بدء التشغيل
 * تم تحديثها لتوفير حماية متقدمة للكود والبيانات
 */
export const secureAppInitialization = (): void => {
  try {
    console.log('بدء تأمين التطبيق...');

    // المرحلة 1: تأمين المفاتيح والتوكن الحساسة
    try {
      console.log('تأمين المفاتيح والتوكن الحساسة...');
      tokenProtection.protectAllSensitiveTokens();
      tokenProtection.hideTokensFromConsole();
    } catch (error) {
      console.error('فشل في تأمين المفاتيح والتوكن:', error);
    }

    // المرحلة 2: تفعيل حماية الكود
    try {
      console.log('تفعيل حماية الكود...');
      codeProtection.initializeProtection();
    } catch (error) {
      console.error('فشل في تفعيل حماية الكود:', error);
    }

    // المرحلة 3: تفعيل حماية الكود من التلاعب
    try {
      console.log('تفعيل حماية الكود من التلاعب...');
      tamperProtection.initializeTamperProtection();
    } catch (error) {
      console.error('فشل في تفعيل حماية الكود من التلاعب:', error);
    }

    // المرحلة 4: ترقية تشفير البيانات المخزنة
    try {
      console.log('ترقية تشفير البيانات المخزنة...');
      secureStorage.upgradeEncryption();
    } catch (error) {
      console.error('فشل في ترقية تشفير البيانات المخزنة:', error);
    }

    // إضافة معالج للأخطاء غير المعالجة
    window.addEventListener('error', (event) => {
      console.error('خطأ غير معالج:', event.error);

      // لا نمنع الأخطاء من الظهور في وضع التطوير
      if (import.meta.env.PROD) {
        event.preventDefault();
      }
    });

    // إضافة معالج للوعود المرفوضة غير المعالجة
    window.addEventListener('unhandledrejection', (event) => {
      console.error('وعد مرفوض غير معالج:', event.reason);

      // لا نمنع الأخطاء من الظهور في وضع التطوير
      if (import.meta.env.PROD) {
        event.preventDefault();
      }
    });

    // تم تعطيل إضافة رؤوس الأمان عبر JavaScript لتجنب التحذيرات
    // سيتم التعامل مع الأمان عبر HTTP headers في Cloudflare
    console.log('Security headers will be handled by Cloudflare Pages');

    console.log('تم تأمين التطبيق بنجاح');
  } catch (error) {
    console.error('فشل في تأمين التطبيق عند بدء التشغيل:', error);
  }
};

// تصدير جميع وحدات الأمان
export default {
  secureStorage,
  secureApi,
  secureExcel,
  secureIO,
  secureFiles,
  secureCommunication,
  codeProtection,
  tokenProtection,
  tamperProtection,
  checkUserPermission,
  isLoggedIn,
  getCurrentUser,
  secureLogout,
  secureAppInitialization
};
