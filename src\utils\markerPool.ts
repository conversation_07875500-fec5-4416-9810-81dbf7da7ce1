/**
 * Marker Pool System for High Performance
 * نظام تجميع العلامات لأداء عالي
 */

import mapboxgl from 'mapbox-gl';
import { GasStation } from '@/types/station';

interface PooledMarker {
  marker: mapboxgl.Marker;
  element: HTMLDivElement;
  isInUse: boolean;
  stationId?: string;
}

class MarkerPool {
  private pool: PooledMarker[] = [];
  private maxPoolSize: number = 200; // Maximum markers to keep in pool
  private minPoolSize: number = 50;  // Minimum markers to pre-create
  
  constructor() {
    this.preCreateMarkers();
  }

  /**
   * Pre-create markers for faster access
   */
  private preCreateMarkers(): void {
    for (let i = 0; i < this.minPoolSize; i++) {
      const element = this.createMarkerElement();
      const marker = new mapboxgl.Marker({
        element,
        anchor: 'bottom',
        offset: [0, 0],
        draggable: false,
      });
      
      this.pool.push({
        marker,
        element,
        isInUse: false
      });
    }
  }

  /**
   * Create optimized marker element
   */
  private createMarkerElement(): HTMLDivElement {
    const el = document.createElement('div');
    el.className = 'marker-pin';
    
    // Pre-set common styles for better performance
    el.style.cssText = `
      width: 28px;
      height: 28px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      cursor: pointer;
      z-index: 1;
      will-change: transform;
      touch-action: manipulation;
      position: absolute;
      transform-origin: center bottom;
    `;
    
    return el;
  }

  /**
   * Get a marker from pool or create new one
   */
  getMarker(station: GasStation, isSelected: boolean = false): PooledMarker {
    // Find available marker in pool
    let pooledMarker = this.pool.find(m => !m.isInUse);
    
    if (!pooledMarker) {
      // Create new marker if pool is empty
      const element = this.createMarkerElement();
      const marker = new mapboxgl.Marker({
        element,
        anchor: 'bottom',
        offset: [0, 0],
        draggable: false,
      });
      
      pooledMarker = {
        marker,
        element,
        isInUse: false
      };
      
      // Add to pool if under max size
      if (this.pool.length < this.maxPoolSize) {
        this.pool.push(pooledMarker);
      }
    }

    // Configure marker for this station
    this.configureMarker(pooledMarker, station, isSelected);
    pooledMarker.isInUse = true;
    pooledMarker.stationId = station.id;
    
    return pooledMarker;
  }

  /**
   * Configure marker for specific station
   */
  private configureMarker(pooledMarker: PooledMarker, station: GasStation, isSelected: boolean): void {
    const { element, marker } = pooledMarker;

    // Validate coordinates
    if (!station.longitude || !station.latitude ||
        isNaN(station.longitude) || isNaN(station.latitude) ||
        station.longitude < -180 || station.longitude > 180 ||
        station.latitude < -90 || station.latitude > 90) {
      console.error(`MarkerPool: Invalid coordinates for station ${station.id}: lat=${station.latitude}, lng=${station.longitude}`);
      return;
    }

    // Detect mobile for size optimization
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isSelected) {
      element.className = 'marker-pin selected mapboxgl-marker mapboxgl-marker-anchor-center';
      element.style.width = isMobile ? '48px' : '40px';
      element.style.height = isMobile ? '48px' : '40px';
      element.style.zIndex = '1000';
      element.style.transform = 'scale(1.2)';
    } else {
      element.className = 'marker-pin';
      element.style.width = isMobile ? '36px' : '28px';
      element.style.height = isMobile ? '36px' : '28px';
      element.style.zIndex = '1';
      element.style.transform = 'scale(1)';
    }

    // Set background image
    element.style.backgroundImage = `url('/lovable-uploads/27c1f136-856b-4b61-b332-3cea9403770a.png')`;

    // Set position with validated coordinates
    console.log(`MarkerPool: Setting marker position for station ${station.id} at [${station.longitude}, ${station.latitude}]`);
    marker.setLngLat([station.longitude, station.latitude]);

    // Set data attributes
    element.dataset.stationId = station.id;
    element.dataset.markerType = isSelected ? 'selected' : 'regular';
  }

  /**
   * Return marker to pool
   */
  returnMarker(pooledMarker: PooledMarker): void {
    if (!pooledMarker.isInUse) return;
    
    // Remove from map
    pooledMarker.marker.remove();
    
    // Reset state
    pooledMarker.isInUse = false;
    pooledMarker.stationId = undefined;
    
    // Reset element
    const { element } = pooledMarker;
    element.className = 'marker-pin';
    element.style.transform = 'scale(1)';
    element.style.zIndex = '1';
    element.removeAttribute('data-station-id');
    element.removeAttribute('data-marker-type');
    
    // Remove event listeners
    const newElement = element.cloneNode(true) as HTMLDivElement;
    element.parentNode?.replaceChild(newElement, element);
    pooledMarker.element = newElement;
    pooledMarker.marker = new mapboxgl.Marker({
      element: newElement,
      anchor: 'bottom',
      offset: [0, 0],
      draggable: false,
    });
  }

  /**
   * Return all markers to pool
   */
  returnAllMarkers(): void {
    this.pool.forEach(pooledMarker => {
      if (pooledMarker.isInUse) {
        this.returnMarker(pooledMarker);
      }
    });
  }

  /**
   * Get pool statistics
   */
  getStats(): { total: number; inUse: number; available: number } {
    const inUse = this.pool.filter(m => m.isInUse).length;
    return {
      total: this.pool.length,
      inUse,
      available: this.pool.length - inUse
    };
  }

  /**
   * Clean up unused markers to free memory
   */
  cleanup(): void {
    const availableMarkers = this.pool.filter(m => !m.isInUse);
    
    // Keep only minimum required markers
    if (availableMarkers.length > this.minPoolSize) {
      const toRemove = availableMarkers.slice(this.minPoolSize);
      toRemove.forEach(pooledMarker => {
        pooledMarker.marker.remove();
      });
      
      this.pool = this.pool.filter(m => !toRemove.includes(m));
    }
  }
}

// Export singleton instance
export const markerPool = new MarkerPool();
export default markerPool;
