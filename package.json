{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "node build.js", "build:vite": "vite build", "build:dev": "vite build --mode development", "build:cloudflare": "node build.js", "copy-cloudflare-files": "copy public\\_headers dist\\_headers && copy public\\_redirects dist\\_redirects", "lint": "eslint .", "preview": "vite preview", "deploy:preview": "npm run build:cloudflare && npm run preview"}, "dependencies": {"@chakra-ui/react": "^3.17.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^3.9.0", "@jest/globals": "^29.7.0", "@mapbox/mapbox-gl-draw": "^1.5.0", "@mui/material": "^7.2.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.56.2", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/react-sound": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "exceljs": "^4.4.0", "express": "^5.1.0", "framer-motion": "^12.10.5", "howler": "^2.2.4", "i18next": "^25.1.2", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "mapbox-gl": "^3.11.1", "mssql": "^11.0.1", "next-themes": "^0.3.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.1", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "supercluster": "^8.0.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "xlsx": "0.18.5", "zod": "^3.23.8", "vite": "^6.3.5", "@vitejs/plugin-react-swc": "^3.5.0", "typescript": "^5.5.3", "tailwindcss": "^3.4.11", "autoprefixer": "^10.4.20", "postcss": "^8.4.47"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/supercluster": "^7.1.3", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "terser": "^5.39.0", "typescript-eslint": "^8.0.1"}}