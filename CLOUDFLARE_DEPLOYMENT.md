# دليل نشر المشروع على Cloudflare Pages

## المشكلة التي تم حلها

كان المشروع يعمل محلياً ولكن يظهر صفحة بيضاء على Cloudflare Pages مع الخطأ:
```
Uncaught TypeError: Cannot read properties of undefined (reading 'createContext')
```

## الحلول المطبقة

### 1. إصلاح مشكلة process.env
- تم تحديث `src/main.tsx` لاستخدام `import.meta.env.DEV` بدلاً من `process.env.NODE_ENV`
- تم تحسين `public/process-shim.js` ليكون أكثر توافقاً مع Cloudflare

### 2. تحديث إعدادات Vite
- تم تحديث `vite.config.ts` لضمان التوافق مع Cloudflare Pages
- تم تغيير target إلى `es2020`
- تم إضافة تعريفات أفضل للمتغيرات العامة

### 3. إضافة ملفات Cloudflare
- `public/_headers`: إعدادات الأمان والتخزين المؤقت
- `public/_redirects`: إعادة توجيه للتوافق مع React Router
- `wrangler.toml`: إعدادات Cloudflare Pages

## خطوات النشر

### 1. بناء المشروع
```bash
npm run build
```

### 2. رفع المشروع إلى GitHub
```bash
git add .
git commit -m "Fix Cloudflare deployment issues"
git push origin main
```

### 3. ربط المشروع بـ Cloudflare Pages
1. اذهب إلى [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. اختر "Pages" من القائمة الجانبية
3. اضغط "Create a project"
4. اختر "Connect to Git"
5. اختر المستودع من GitHub
6. استخدم الإعدادات التالية:
   - **Build command**: `npm run build`
   - **Build output directory**: `dist`
   - **Root directory**: `/` (أو اتركه فارغاً)

### 4. متغيرات البيئة
في إعدادات Cloudflare Pages، أضف المتغيرات التالية:
```
NODE_ENV=production
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_MAPBOX_TOKEN=your_mapbox_token
```

## الملفات المهمة للنشر

### الملفات الأساسية:
- `dist/` - مجلد البناء النهائي
- `public/_headers` - إعدادات الأمان
- `public/_redirects` - إعادة التوجيه
- `public/process-shim.js` - دعم process في المتصفح
- `wrangler.toml` - إعدادات Cloudflare

### ملفات الإعدادات:
- `vite.config.ts` - إعدادات البناء
- `package.json` - التبعيات والسكريبتات
- `tsconfig.json` - إعدادات TypeScript

## نصائح مهمة

1. **تأكد من متغيرات البيئة**: جميع المتغيرات يجب أن تبدأ بـ `VITE_`
2. **استخدم HTTPS**: Cloudflare يتطلب HTTPS للميزات المتقدمة
3. **تحقق من الكونسول**: استخدم أدوات المطور للتحقق من الأخطاء
4. **اختبر محلياً**: تأكد من أن `npm run build && npm run preview` يعمل

## استكشاف الأخطاء

### إذا ظهرت صفحة بيضاء:
1. تحقق من الكونسول في أدوات المطور
2. تأكد من أن جميع متغيرات البيئة مضبوطة
3. تحقق من أن ملف `process-shim.js` يتم تحميله

### إذا لم تعمل الروابط:
1. تأكد من وجود ملف `_redirects`
2. تحقق من إعدادات React Router

### إذا كانت هناك مشاكل في الأمان:
1. تحقق من ملف `_headers`
2. راجع إعدادات Content Security Policy

## الدعم

إذا واجهت مشاكل، تحقق من:
- [Cloudflare Pages Documentation](https://developers.cloudflare.com/pages/)
- [Vite Deployment Guide](https://vitejs.dev/guide/static-deploy.html)
- سجلات البناء في Cloudflare Dashboard
